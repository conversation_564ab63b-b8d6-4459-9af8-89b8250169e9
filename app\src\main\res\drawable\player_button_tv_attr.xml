<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="?attr/white" />
            <corners android:radius="@dimen/rounded_image_radius"/>
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white_attr_20" />
            <corners android:radius="@dimen/rounded_image_radius"/>
        </shape>
    </item>
</selector>