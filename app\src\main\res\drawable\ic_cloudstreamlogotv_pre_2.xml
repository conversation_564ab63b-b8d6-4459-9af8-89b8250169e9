<vector xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:aapt="http://schemas.android.com/aapt"
        android:width="907.09dp"
        android:height="510.24dp"
        android:viewportWidth="907.09"
        android:viewportHeight="510.24">
    <path
            android:strokeWidth="1"
            android:pathData="M-20.09,-10.91h949.79v534.26h-949.79z"
            android:fillColor="#121212"
            android:strokeColor="#fff" />
    <path android:pathData="M273.68,250.58a18.79,18.79 0,0 0,-5.64 0.86,26.56 26.56,0 0,0 -24.73,-21.51 40.83,40.83 0,0 0,-68.95 -13.08A32.07,32.07 0,0 0,132 247.24a30.92,30.92 0,0 0,0.18 3.35H132a18.9,18.9 0,0 0,0 37.8H273.68a18.9,18.9 0,0 0,0 -37.8Z">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startY="245.72"
                    android:startX="113.12"
                    android:endY="245.72"
                    android:endX="292.58"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#DB0909" />
                <item
                        android:offset="1"
                        android:color="#A10808" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M248.76,234.41c0,-1.22 0,-2.42 -0.09,-3.63a27,27 0,0 0,-5.36 -0.85,40.83 40.83,0 0,0 -68.95,-13.08A32.07,32.07 0,0 0,132 247.24a30.92,30.92 0,0 0,0.18 3.35H132a18.9,18.9 0,0 0,0 37.8H228.5A81.75,81.75 0,0 0,248.76 234.41Z">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startY="245.72"
                    android:startX="113.12"
                    android:endY="245.72"
                    android:endX="248.76"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#E23A3A" />
                <item
                        android:offset="1"
                        android:color="#DD1130" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M174.36,216.85A32.07,32.07 0,0 0,132 247.24a30.92,30.92 0,0 0,0.18 3.35H132a18.89,18.89 0,0 0,-1.18 37.74A81.53,81.53 0,0 0,210 206.83c0,-1.15 0,-2.29 -0.09,-3.43a41.33,41.33 0,0 0,-5 -0.33A40.71,40.71 0,0 0,174.36 216.85Z">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startY="245.69"
                    android:startX="113.12"
                    android:endY="245.69"
                    android:endX="210.03"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#E44D4D" />
                <item
                        android:offset="1"
                        android:color="#E76161" />
            </gradient>
        </aapt:attr>
    </path>

    <path
            android:pathData="M358.81,285q-13.53,0 -22.64,-9.1t-9,-22.72q0,-13.62 9,-22.64 9,-9.18 22.64,-9.19 13.79,0 22.38,10l-5.62,5.44a20.82,20.82 0,0 0,-16.76 -7.91,23 23,0 0,0 -16.94,6.81q-6.72,6.72 -6.72,17.53t6.72,17.53a23,23 0,0 0,16.94 6.81q10.63,0 18.46,-8.94l5.7,5.53a29.57,29.57 0,0 1,-10.63 8A32.44,32.44 0,0 1,358.81 285Z"
            android:fillColor="#E23A3A" />
    <path
            android:pathData="M397.78,222.69v60.93H390V222.69Z"
            android:fillColor="#E23A3A" />
    <path
            android:pathData="M404.5,262.77q0,-9.61 6,-15.91a20.6,20.6 0,0 1,15.41 -6.3,20.31 20.31,0 0,1 15.31,6.3 21.87,21.87 0,0 1,6.13 15.91q0,9.71 -6.13,15.92A20.3,20.3 0,0 1,426 285a20.6,20.6 0,0 1,-15.41 -6.29Q404.5,272.39 404.5,262.77ZM412.33,262.77a15.31,15.31 0,0 0,3.91 10.9,13.38 13.38,0 0,0 19.41,0 17,17 0,0 0,0 -21.7,13.18 13.18,0 0,0 -19.41,0A15.18,15.18 0,0 0,412.33 262.77Z"
            android:fillColor="#E23A3A" />
    <path
            android:pathData="M490.7,283.62h-7.48v-5.78h-0.35a13.86,13.86 0,0 1,-5.48 5.1,15.77 15.77,0 0,1 -7.7,2q-7.67,0 -11.79,-4.38t-4.13,-12.47v-26.2h7.83v25.69q0.25,10.22 10.3,10.22a9.81,9.81 0,0 0,7.83 -3.79,13.7 13.7,0 0,0 3.14,-9.06V241.93h7.83Z"
            android:fillColor="#E23A3A" />
    <path
            android:pathData="M517.25,285a18.34,18.34 0,0 1,-14 -6.46,24.34 24.34,0 0,1 0,-31.49 18.35,18.35 0,0 1,14 -6.47,18.07 18.07,0 0,1 8.39,2 14.84,14.84 0,0 1,5.83 5.19h0.34l-0.34,-5.78L531.47,222.69h7.82v60.93h-7.48v-5.78h-0.34a14.84,14.84 0,0 1,-5.83 5.19A18.07,18.07 0,0 1,517.25 285ZM518.53,277.86a12,12 0,0 0,9.45 -4.17q3.82,-4.17 3.83,-10.9A15.54,15.54 0,0 0,528 252a12.05,12.05 0,0 0,-9.45 -4.26,12.19 12.19,0 0,0 -9.44,4.26 15.5,15.5 0,0 0,-3.83 10.8,15.32 15.32,0 0,0 3.83,10.81A12.19,12.19 0,0 0,518.53 277.84Z"
            android:fillColor="#E23A3A" />
    <path
            android:pathData="M587.8,267.33a15.91,15.91 0,0 1,-5.87 12.88A22.43,22.43 0,0 1,567.46 285a21.39,21.39 0,0 1,-13.36 -4.42,22.65 22.65,0 0,1 -8,-12.08l7.49,-3.07a19.3,19.3 0,0 0,2.13 4.94,15.72 15.72,0 0,0 3.19,3.78 14.25,14.25 0,0 0,4 2.47,12.26 12.26,0 0,0 4.68,0.9 13.47,13.47 0,0 0,8.76 -2.77,9 9,0 0,0 3.41,-7.36 8.8,8.8 0,0 0,-2.81 -6.55q-2.64,-2.64 -9.87,-5.11 -7.32,-2.64 -9.11,-3.57 -9.69,-4.94 -9.7,-14.55a14.84,14.84 0,0 1,5.37 -11.49A19.53,19.53 0,0 1,567 221.33a20.5,20.5 0,0 1,12.09 3.58,16.67 16.67,0 0,1 6.8,8.76l-7.31,3.06a10.84,10.84 0,0 0,-4 -5.65,13.1 13.1,0 0,0 -15.11,0.28 7.41,7.41 0,0 0,-3.15 6.19,7.14 7.14,0 0,0 2.47,5.42q2.73,2.29 11.83,5.42 9.27,3.17 13.23,7.72A16.53,16.53 0,0 1,587.8 267.33Z"
            android:fillColor="#E76161" />
    <path
            android:pathData="M610.26,284.3a11.88,11.88 0,0 1,-8.46 -3.15c-2.25,-2.09 -3.4,-5 -3.45,-8.76V249.07H591v-7.14h7.32V229.16h7.83v12.77h10.21v7.14H606.18v20.77c0,2.78 0.54,4.66 1.61,5.66a5.27,5.27 0,0 0,3.66 1.48,7.9 7.9,0 0,0 1.83,-0.21 9,9 0,0 0,1.66 -0.55l2.47,7A21.23,21.23 0,0 1,610.26 284.3Z"
            android:fillColor="#E76161" />
    <path
            android:pathData="M631.71,283.62h-7.83V241.93h7.48v6.8h0.35a11.31,11.31 0,0 1,4.89 -5.66,13.66 13.66,0 0,1 7.27,-2.34 14.7,14.7 0,0 1,5.79 1l-2.38,7.57a12.93,12.93 0,0 0,-4.6 -0.6,10.11 10.11,0 0,0 -7.7,3.58 12,12 0,0 0,-3.27 8.34Z"
            android:fillColor="#E76161" />
    <path
            android:pathData="M670.93,285a19.93,19.93 0,0 1,-15.14 -6.29q-6,-6.3 -6,-15.92a22.65,22.65 0,0 1,5.79 -15.87,19.15 19.15,0 0,1 14.8,-6.34q9.29,0 14.77,6t5.49,16.81l-0.09,0.85L657.83,264.24a13.56,13.56 0,0 0,4.08 9.87,13.06 13.06,0 0,0 9.36,3.75q7.49,0 11.75,-7.49l7,3.4a20.69,20.69 0,0 1,-7.78 8.25A21.51,21.51 0,0 1,670.93 285ZM658.42,257.77h23.92a10.43,10.43 0,0 0,-3.53 -7.19,12.38 12.38,0 0,0 -8.56,-2.85 11.34,11.34 0,0 0,-7.61 2.72A13.09,13.09 0,0 0,658.42 257.75Z"
            android:fillColor="#E76161" />
    <path
            android:pathData="M714.08,240.56q8.67,0 13.7,4.64c3.34,3.1 5,7.33 5,12.72v25.7h-7.49v-5.78H725Q720.11,285 712,285a16.83,16.83 0,0 1,-11.53 -4.08,13 13,0 0,1 -4.63,-10.21 12.38,12.38 0,0 1,4.89 -10.3q4.89,-3.83 13.06,-3.83a23.16,23.16 0,0 1,11.49 2.55v-1.78a8.9,8.9 0,0 0,-3.24 -6.94,11.08 11.08,0 0,0 -7.57,-2.85 12,12 0,0 0,-10.38 5.53l-6.89,-4.34Q702.93,240.57 714.08,240.56ZM704,270.86a6.24,6.24 0,0 0,2.59 5.1,9.57 9.57,0 0,0 6.09,2.05 12.5,12.5 0,0 0,8.81 -3.66,11.47 11.47,0 0,0 3.87,-8.6q-3.66,-2.88 -10.21,-2.89a13.22,13.22 0,0 0,-8 2.3A6.81,6.81 0,0 0,704 270.86Z"
            android:fillColor="#E76161" />
    <path
            android:pathData="M749.47,283.62h-7.82V241.93h7.48v5.78h0.34a14,14 0,0 1,5.49 -5.1,15.06 15.06,0 0,1 7.36,-2.05 15.22,15.22 0,0 1,8.09 2.13,12.56 12.56,0 0,1 5.1,5.87q5.19,-8 14.39,-8 7.23,0 11.14,4.43T805,257.58v26h-7.83V258.77q0,-5.86 -2.13,-8.46t-7.15,-2.6a9.35,9.35 0,0 0,-7.57 3.83,14 14,0 0,0 -3.06,9v23.06h-7.83V258.77q0,-5.86 -2.13,-8.46t-7.15,-2.6a9.35,9.35 0,0 0,-7.57 3.83,14 14,0 0,0 -3.07,9Z"
            android:fillColor="#E76161" />

    <path android:pathData="M-13.76,555.76c10.3,-20.89 58.91,-113.94 157.31,-139.7C261.3,385.24 405.9,462.43 469.89,613.28">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startY="252.3"
                    android:startX="194.11"
                    android:endY="252.3"
                    android:endX="373.57"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#E76161" />
                <item
                        android:offset="1"
                        android:color="#E23A3A" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M318.2,592.15c52.89,-55.46 139,-131.3 263,-187.83 223.69,-102 495.29,-119.94 515.35,-62.21 13,37.39 -73.5,124.43 -496.69,339.65">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startX="400.11"
                    android:endX="900"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#E76161" />
                <item
                        android:offset="1"
                        android:color="#E23A3A" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M-57.58,195c206.91,86.6 494,-219.13 453.91,-347.48C353.52,-289.67 -103.15,-353.41 -203.15,-176 -265.5,-65.35 -189.57,139.73 -57.58,195Z">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startY="252.3"
                    android:startX="-100"
                    android:endY="252.3"
                    android:endX="373.57"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#E76161" />
                <item
                        android:offset="1"
                        android:color="#E23A3A" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M698.42,648.89C625.71,546 764,320.79 920.68,218.45c46.61,-30.44 110.17,-72 164.35,-50.08 102.25,41.28 158.19,303.22 28.17,446.08C996.65,742.52 762.64,739.78 698.42,648.89Z">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startX="700.11"
                    android:endX="900.57"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#E76161" />
                <item
                        android:offset="1"
                        android:color="#E23A3A" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M339.91,-42.46a246.52,141.46 0,1 0,493.04 0a246.52,141.46 0,1 0,-493.04 0z">
        <aapt:attr name="android:fillColor">
            <gradient
                    android:startX="400.11"
                    android:endX="800.57"
                    android:type="linear">
                <item
                        android:offset="0"
                        android:color="#E76161" />
                <item
                        android:offset="1"
                        android:color="#E23A3A" />
            </gradient>
        </aapt:attr>
    </path>
</vector>
