<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:background="?attr/primaryGrayBackground"
        android:id="@+id/settings_top_root"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <include layout="@layout/standard_toolbar" />

    <!-- Required ViewGroup for PreferenceFragmentCompat -->
    <FrameLayout
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            android:background="?attr/primaryBlackBackground"
            android:id="@android:id/list_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>