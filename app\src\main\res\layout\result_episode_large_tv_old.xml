<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    android:id="@+id/episode_holder_large"

    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="@drawable/outline_drawable"
    app:cardBackgroundColor="?attr/boxItemBackground"

    app:cardCornerRadius="@dimen/rounded_image_radius">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/outline_drawable"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <!--app:cardCornerRadius="@dimen/roundedImageRadius"-->
            <androidx.cardview.widget.CardView
                android:layout_width="126dp"
                android:layout_height="72dp"
                android:foreground="@drawable/outline_drawable">

                <ImageView
                    android:id="@+id/episode_poster"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"

                    android:contentDescription="@string/episode_poster_img_des"
                    android:foreground="?android:attr/selectableItemBackgroundBorderless"
                    android:scaleType="centerCrop"
                    tools:src="@drawable/example_poster" />

                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/play_episode"
                    android:src="@drawable/play_button" />

                <androidx.core.widget.ContentLoadingProgressBar
                    android:id="@+id/episode_progress"
                    style="@android:style/Widget.Material.ProgressBar.Horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="5dp"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="-1.5dp"
                    android:progressBackgroundTint="?attr/colorPrimary"
                    android:progressTint="?attr/colorPrimary"
                    tools:progress="50" />
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="50dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/episode_filler"
                        style="@style/SmallWhiteButton"
                        android:layout_gravity="start"
                        android:layout_marginEnd="10dp"
                        android:text="@string/filler" />

                    <TextView
                        android:id="@+id/episode_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textColor="?attr/textColor"
                        android:textStyle="bold"
                        tools:text="1. Jobless" />
                </LinearLayout>

                <TextView
                    android:id="@+id/episode_rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/grayTextColor"
                    tools:text="Rated: 8.8" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/episode_descript"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="4"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="?attr/grayTextColor"
            tools:text="Jon and Daenerys arrive in Winterfell and are met with skepticism. Sam learns about the fate of his family. Cersei gives Euron the reward he aims for. Theon follows his heart. Jon and Daenerys arrive in Winterfell and are met with skepticism. Sam learns about the fate of his family. Cersei gives Euron the reward he aims for. Theon follows his heart." />
    </LinearLayout>
</androidx.cardview.widget.CardView>