<?xml version="1.0" encoding="utf-8"?>
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
        android:fromDegrees="270"
        android:toDegrees="270">
    <shape
            android:innerRadiusRatio="2.5"
            android:shape="ring"
            android:thickness="2dp"
            android:useLevel="true"><!-- this line fixes the issue for lollipop api 21 -->

        <gradient
                android:angle="0"
                android:endColor="?attr/colorPrimary"
                android:startColor="?attr/colorPrimary"
                android:type="sweep"
                android:useLevel="false" />
    </shape>
</rotate>