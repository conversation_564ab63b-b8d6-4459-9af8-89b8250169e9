<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <PreferenceCategory android:title="@string/pref_category_looks">
        <Preference
            android:icon="@drawable/ic_baseline_color_lens_24"
            android:key="@string/primary_color_key"
            android:title="@string/primary_color_settings" />
        <Preference
            android:icon="@drawable/ic_baseline_color_lens_24"
            android:key="@string/app_theme_key"
            android:title="@string/app_theme_settings" />
        <Preference
            android:icon="@drawable/ic_baseline_tv_24"
            android:key="@string/app_layout_key"
            android:title="@string/app_layout" />
        <SwitchPreference
            android:defaultValue="true"
            android:icon="@drawable/title_24px"
            android:key="@string/bottom_title_key"
            android:summary="@string/bottom_title_settings_des"
            android:title="@string/bottom_title_settings" />

        <SeekBarPreference
            android:defaultValue="0"
            android:icon="@drawable/arrows_input_24px"
            android:key="@string/overscan_key"
            android:max="100"
            android:summary="@string/overscan_settings_des"
            android:title="@string/overscan_settings"
            app:adjustable="true"
            app:min="0"
            app:seekBarIncrement="1"
            app:showSeekBarValue="true" />

        <SeekBarPreference
            android:defaultValue="0"
            android:icon="@drawable/baseline_grid_view_24"
            android:key="@string/poster_size_key"
            android:max="15"
            android:summary="@string/poster_size_settings_des"
            android:title="@string/poster_size_settings"
            app:adjustable="true"
            app:min="0"
            app:seekBarIncrement="1"
            app:showSeekBarValue="true" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_category_ui_features">
        <Preference
            android:icon="@drawable/ic_baseline_tv_24"
            android:key="@string/poster_ui_key"
            android:title="@string/poster_ui_settings" />
        <SwitchPreference
            android:icon="@drawable/search_icon"
            android:summary="@string/advanced_search_des"
            android:title="@string/advanced_search"
            app:defaultValue="true"
            app:key="advanced_search" />
        <SwitchPreference
            android:defaultValue="true"
            android:icon="@drawable/baseline_theaters_24"
            android:key="@string/show_trailers_key"
            android:title="@string/show_trailers_settings" />
        <SwitchPreference
            android:defaultValue="true"
            android:icon="@drawable/kitsu_icon"
            android:key="@string/show_kitsu_posters_key"
            android:title="@string/kitsu_settings" />
        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_baseline_skip_next_24"
            android:key="@string/show_fillers_key"
            android:title="@string/show_fillers_settings" />
        <SwitchPreference
            android:icon="@drawable/ic_baseline_play_arrow_24"
            android:summary="@string/random_button_settings_desc"
            android:title="@string/random_button_settings"
            app:defaultValue="false"
            app:key="@string/random_button_key" />
        <Preference
            android:icon="@drawable/ic_baseline_exit_24"
            android:key="@string/confirm_exit_key"
            android:summary="@string/confirm_before_exiting_desc"
            android:title="@string/confirm_before_exiting_title" />
        <Preference
            android:icon="@drawable/ic_baseline_filter_list_24"
            android:key="@string/pref_filter_search_quality_key"
            android:title="@string/pref_filter_search_quality" />
    </PreferenceCategory>
</PreferenceScreen>