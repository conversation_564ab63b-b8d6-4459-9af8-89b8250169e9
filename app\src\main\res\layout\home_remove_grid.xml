<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/background_card"
    android:layout_width="114dp"
    android:layout_height="180dp"
    android:layout_margin="2dp"
    android:layout_marginBottom="2dp"
    android:elevation="10dp"
    android:foreground="@drawable/outline_drawable"
    app:cardBackgroundColor="?attr/primaryGrayBackground"
    app:cardCornerRadius="@dimen/rounded_image_radius">

    <ImageView
        android:id="@+id/title_shadow"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom"
        android:clickable="false"
        android:focusable="false"
        android:src="@drawable/title_shadow"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/imageText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:paddingStart="5dp"
        android:paddingTop="5dp"
        android:paddingEnd="5dp"
        android:paddingBottom="5dp"
        android:text="@string/clear_history"
        android:textColor="@color/textColor"
        android:textStyle="bold" />

    <ImageView
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="center"
        android:src="@drawable/delete_all" />

</androidx.cardview.widget.CardView>
