<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/download_child_episode_holder"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:layout_marginBottom="5dp"
    android:foreground="@drawable/outline_drawable"
    android:focusable="true"
    android:nextFocusLeft="@id/nav_rail_view"
    android:nextFocusRight="@id/download_button"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="@dimen/rounded_image_radius"
    app:cardElevation="0dp">

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/download_child_episode_progress"
        style="@android:style/Widget.Material.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="-1.5dp"
        android:progressBackgroundTint="?attr/colorPrimary"
        android:progressTint="?attr/colorPrimary"
        tools:progress="50" />

    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:foreground="?android:attr/selectableItemBackgroundBorderless">

        <ImageView
            android:id="@+id/download_child_episode_play"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:contentDescription="@string/episode_play_img_des"
            android:src="@drawable/ic_baseline_play_arrow_24"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="50dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/download_child_episode_text"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:ellipsize="marquee"
                android:gravity="center_vertical"
                android:marqueeRepeatLimit="marquee_forever"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:textColor="?attr/textColor"
                tools:text="Episode 1 Episode 1 Episode 1 Episode 1 Episode 1 Episode 1 Episode 1" />

            <TextView
                android:id="@+id/download_child_episode_text_extra"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:gravity="center_vertical"
                android:textColor="?attr/grayTextColor"
                tools:text="128MB / 237MB" />
        </LinearLayout>

        <com.lagradost.cloudstream3.ui.download.button.PieFetchButton
            android:id="@+id/download_button"
            android:layout_width="@dimen/download_size"
            android:layout_height="@dimen/download_size"
            android:layout_gravity="center_vertical|end"
            android:layout_marginStart="-50dp"
            android:foreground="@drawable/outline_drawable"
            android:focusable="true"
            android:nextFocusLeft="@id/download_child_episode_holder"
            android:padding="10dp" />

        <CheckBox
            android:id="@+id/delete_checkbox"
            android:layout_width="@dimen/download_size"
            android:layout_height="@dimen/download_size"
            android:layout_gravity="center_vertical|end"
            android:layout_marginStart="-50dp"
            android:focusable="true"
            android:nextFocusLeft="@id/download_child_episode_holder"
            android:padding="10dp"
            android:visibility="gone" />
    </GridLayout>
</androidx.cardview.widget.CardView>