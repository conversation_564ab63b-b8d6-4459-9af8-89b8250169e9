<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/text1"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_rowWeight="1"
            android:layout_gravity="center_vertical"

            android:paddingStart="?android:attr/listPreferredItemPaddingStart"
            android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
            android:text="@string/add_repository"
            android:textColor="?attr/textColor"
            android:textSize="20sp"
            android:textStyle="bold" />

<!--        <com.google.android.material.button.MaterialButton-->
<!--            android:nextFocusDown="@id/repo_name_input"-->
<!--            android:id="@+id/list_repositories"-->
<!--            android:nextFocusLeft="@id/apply_btt"-->
<!--            android:nextFocusRight="@id/cancel_btt"-->
<!--            style="@style/WhiteButton"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            android:text="@string/view_public_repositories_button_short" />-->
    </LinearLayout>

    <TextView
        android:id="@+id/text2"
        android:layout_width="match_parent"

        android:layout_height="wrap_content"
        android:layout_rowWeight="1"
        android:layout_gravity="center_vertical"

        android:layout_marginBottom="10dp"
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"

        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
        android:textColor="?attr/grayTextColor"
        android:textSize="15sp"
        android:visibility="gone"
        tools:text="Gogoanime" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="60dp"
        android:orientation="vertical">

        <EditText
            android:id="@+id/repo_name_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autofillHints="username"
            android:hint="@string/repository_name_hint"
            android:inputType="text"
            android:nextFocusLeft="@id/apply_btt"
            android:nextFocusRight="@id/cancel_btt"
            android:nextFocusDown="@id/repo_url_input"
            android:requiresFadingEdge="vertical"
            android:textColorHint="?attr/grayTextColor"
            tools:ignore="LabelFor" />

        <EditText
            android:id="@+id/repo_url_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/repository_url_hint"
            android:inputType="textUri"
            android:nextFocusLeft="@id/apply_btt"
            android:nextFocusRight="@id/cancel_btt"
            android:nextFocusUp="@id/repo_name_input"
            android:nextFocusDown="@id/apply_btt"
            android:requiresFadingEdge="vertical"
            android:textColorHint="?attr/grayTextColor"
            tools:ignore="LabelFor" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/apply_btt_holder"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:layout_marginTop="-60dp"
        android:gravity="bottom|end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/apply_btt"
            style="@style/WhiteButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/add_repository" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel_btt"
            style="@style/BlackButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/sort_cancel" />
    </LinearLayout>
</LinearLayout>