<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:id="@+id/result_sync_holder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/result_sync_names"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="My<PERSON><PERSON><PERSON><PERSON>ist, AniList"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/result_sync_sub_episode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:padding="10dp"
                    android:src="@drawable/baseline_remove_24"
                    app:tint="?attr/textColor" />

                <EditText
                    android:id="@+id/result_sync_current_episodes"
                    style="@style/AppEditStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:textColorHint="?attr/grayTextColor"
                    android:textSize="20sp"
                    tools:hint="20"
                    tools:ignore="LabelFor" />

                <TextView
                    android:id="@+id/result_sync_max_episodes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingBottom="1dp"
                    android:textColor="?attr/textColor"
                    android:textSize="20sp"
                    tools:text="30" />

                <ImageView
                    android:id="@+id/result_sync_add_episode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:padding="10dp"
                    android:src="@drawable/ic_baseline_add_24"
                    app:tint="?attr/textColor" />
            </LinearLayout>

            <androidx.core.widget.ContentLoadingProgressBar
                android:id="@+id/result_sync_episodes"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_gravity="end|center_vertical"
                android:indeterminate="false"
                android:max="100"
                android:padding="10dp"
                android:progress="0"
                android:progressBackgroundTint="?attr/colorPrimary"
                tools:visibility="visible" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:padding="10dp"
                    android:text="@string/sync_score"
                    android:textColor="?attr/textColor"
                    android:textSize="17sp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/result_sync_score_text"

                    style="@style/BlackButton"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="0dp"
                    android:minWidth="0dp"
                    android:text="7/10" />
            </LinearLayout>

            <com.google.android.material.slider.Slider
                android:id="@+id/result_sync_rating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="-5dp"
                android:layout_marginEnd="-5dp"
                app:thumbHeight="20dp"
                android:stepSize="1"
                android:value="4"
                android:valueFrom="0"
                android:valueTo="10"
                app:labelStyle="@style/BlackLabel"
                app:thumbRadius="10dp"
                app:tickVisible="false" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/home_parent_item_title"
                    style="@style/WatchHeaderText"
                    tools:text="Recommended" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="end|center_vertical"
                    android:layout_marginEnd="5dp"
                    android:contentDescription="@string/home_more_info"
                    android:src="@drawable/ic_baseline_arrow_forward_24"
                    app:tint="?attr/textColor" />
            </FrameLayout>

            <ListView
                android:id="@+id/result_sync_check"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_rowWeight="1"
                tools:listitem="@layout/sort_bottom_single_choice" />

            <com.google.android.material.button.MaterialButton
                style="@style/WhiteButton"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                android:text="@string/type_watching"
                android:visibility="gone" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/result_sync_set_score"
                style="@style/BlackButton"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                android:text="@string/upload_sync"
                app:icon="@drawable/baseline_sync_24" />
        </LinearLayout>

    </ScrollView>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/result_sync_loading_shimmer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:padding="15dp"
        app:shimmer_auto_start="true"
        app:shimmer_base_alpha="0.2"
        app:shimmer_duration="@integer/loading_time"
        app:shimmer_highlight_alpha="0.3"
        tools:visibility="gone"
        tools:ignore="MissingClass">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Space
                android:layout_width="match_parent"
                android:layout_height="30dp" />

            <include layout="@layout/loading_line_short" />

            <include layout="@layout/loading_line" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="30dp" />

            <include layout="@layout/loading_line_short" />

            <include layout="@layout/loading_line" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="30dp" />

            <include layout="@layout/loading_line_short" />

            <include layout="@layout/loading_line_short" />

            <include layout="@layout/loading_line_short" />

            <include layout="@layout/loading_line_short" />

            <include layout="@layout/loading_line_short" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="30dp" />

            <include layout="@layout/loading_line" />

            <include layout="@layout/loading_line" />

        </LinearLayout>
    </com.facebook.shimmer.ShimmerFrameLayout>
</FrameLayout>