<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/no_subtitles_loaded_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textSize="18sp"
            android:textStyle="italic"
            android:alpha="0.7"
            android:text="@string/no_subtitles_loaded">
        </TextView>

        <androidx.recyclerview.widget.RecyclerView
            tools:visibility="gone"
            android:id="@+id/subtitle_offset_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingVertical="10dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/subtitle_offset_item">

        </androidx.recyclerview.widget.RecyclerView>
    </FrameLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="410dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                style="@style/WatchHeaderText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="0dp"
                android:paddingTop="10dp"
                android:text="@string/subtitle_offset_title" />

            <TextView
                android:id="@+id/subtitle_offset_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="?attr/grayTextColor"
                tools:text="@string/subtitle_offset_extra_hint_none_format" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/subtitle_offset_subtract_more"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:focusable="true"
                    android:nextFocusRight="@id/subtitle_offset_subtract"
                    android:padding="10dp"
                    android:src="@drawable/ic_baseline_keyboard_arrow_left_24"
                    app:tint="?attr/white"
                    tools:ignore="ContentDescription" />

                <ImageView
                    android:id="@+id/subtitle_offset_subtract"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="10dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:focusable="true"
                    android:nextFocusLeft="@id/subtitle_offset_subtract_more"
                    android:padding="10dp"
                    android:src="@drawable/baseline_remove_24"
                    app:tint="?attr/white"
                    tools:ignore="ContentDescription" />

                <EditText
                    android:id="@+id/subtitle_offset_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:autofillHints="no"
                    android:gravity="center"
                    android:hint="@string/subtitle_offset_hint"
                    android:inputType="numberSigned"
                    tools:ignore="LabelFor" />

                <ImageView
                    android:id="@+id/subtitle_offset_add"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="10dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:focusable="true"
                    android:nextFocusRight="@id/subtitle_offset_add_more"
                    android:padding="10dp"
                    android:src="@drawable/ic_baseline_add_24"
                    app:tint="?attr/white"
                    tools:ignore="ContentDescription" />

                <ImageView
                    android:id="@+id/subtitle_offset_add_more"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:focusable="true"
                    android:nextFocusLeft="@id/subtitle_offset_add"
                    android:nextFocusDown="@id/apply_btt"
                    android:padding="10dp"

                    android:src="@drawable/ic_baseline_keyboard_arrow_right_24"
                    app:tint="?attr/white"
                    tools:ignore="ContentDescription" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_gravity="bottom"
            android:gravity="bottom|end"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/apply_btt"
                style="@style/WhiteButton"
                android:layout_width="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:text="@string/sort_apply"
                android:visibility="visible">

                <requestFocus />
            </com.google.android.material.button.MaterialButton>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/reset_btt"
                style="@style/BlackButton"
                android:layout_width="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:text="@string/reset_btn" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/cancel_btt"
                style="@style/BlackButton"
                android:layout_width="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:text="@string/sort_cancel" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>
