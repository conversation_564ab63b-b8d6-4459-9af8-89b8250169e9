<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">

        <TextView
            android:id="@+id/text1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_rowWeight="1"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:text="@string/stream"
            android:textColor="?attr/textColor"
            android:textSize="20sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/stream_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="20"
            android:hint="@string/network_adress_example"
            android:inputType="textUri"
            tools:ignore="LabelFor" />

        <EditText
            android:id="@+id/stream_referer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="20"
            android:hint="@string/referer"
            android:inputType="textUri"
            tools:ignore="LabelFor" />

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/hls_switch"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:text="@string/hls_playlist"
            android:textColor="?attr/textColor"
            android:textSize="16sp"
            android:visibility="invisible" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:gravity="bottom|end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/apply_btt"
            style="@style/WhiteButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/home_play"
            android:visibility="visible">

            <requestFocus />
        </com.google.android.material.button.MaterialButton>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel_btt"
            style="@style/BlackButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/sort_cancel" />
    </LinearLayout>
</LinearLayout>