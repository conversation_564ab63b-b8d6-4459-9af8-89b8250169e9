<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/navigation_home">
    <action
        android:id="@+id/global_to_navigation_results_tv"
        app:destination="@id/navigation_results_tv"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="url"
            app:argType="string" />
        <argument
            android:name="apiName"
            app:argType="string" />
        <argument
            android:name="startAction"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="startValue"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="restart"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>
    <action
        android:id="@+id/global_to_navigation_results_phone"
        app:destination="@id/navigation_results_phone"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="url"
            app:argType="string" />
        <argument
            android:name="apiName"
            app:argType="string" />
        <argument
            android:name="name"
            app:argType="string" />
        <argument
            android:name="startAction"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="startValue"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="restart"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>
    <action
        android:id="@+id/global_to_navigation_player"
        app:destination="@id/navigation_player"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="data"
            android:defaultValue="@null"
            app:argType="string" />
        <argument
            android:name="uriData"
            android:defaultValue="@null"
            app:argType="string" />
        <argument
            android:name="resumePosition"
            android:defaultValue="0L"
            app:argType="long" />
    </action>

    <action
        android:id="@+id/global_to_navigation_home"
        app:destination="@id/navigation_home"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />

    <action
        android:id="@+id/global_to_navigation_subtitles"
        app:destination="@id/navigation_subtitles"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="hide"
            android:defaultValue="true"
            app:argType="boolean" />
    </action>

    <action
        android:id="@+id/global_to_navigation_chrome_subtitles"
        app:destination="@id/navigation_chrome_subtitles"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="hide"
            android:defaultValue="true"
            app:argType="boolean" />
    </action>

    <action
        android:id="@+id/global_to_navigation_settings_plugins"
        app:destination="@id/navigation_settings_plugins"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="name"
            android:defaultValue="@null"
            app:argType="string" />
        <argument
            android:name="url"
            android:defaultValue="@null"
            app:argType="string" />
        <argument
            android:name="isLocal"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>

    <fragment
        android:id="@+id/navigation_settings_player"
        android:name="com.lagradost.cloudstream3.ui.settings.SettingsPlayer"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <action
            android:id="@+id/action_navigation_settings_player_to_navigation_subtitles"
            app:destination="@id/navigation_subtitles"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
        <action
            android:id="@+id/action_navigation_settings_player_to_navigation_chrome_subtitles"
            app:destination="@id/navigation_chrome_subtitles"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_settings_ui"
        android:name="com.lagradost.cloudstream3.ui.settings.SettingsUI"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />

    <fragment
        android:id="@+id/navigation_library"
        android:name="com.lagradost.cloudstream3.ui.library.LibraryFragment"
        android:label="@string/library"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />

    <fragment
        android:id="@+id/navigation_settings_general"
        android:name="com.lagradost.cloudstream3.ui.settings.SettingsGeneral"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" >
        <action
            android:id="@+id/action_navigation_settings_general_to_easterEggMonkeFragment"
            app:destination="@id/easterEggMonkeFragment"
            app:enterAnim="@anim/go_right"
            app:exitAnim="@anim/go_left"
            app:popEnterAnim="@anim/go_right"
            app:popExitAnim="@anim/go_left" />
    </fragment>

    <fragment
        android:id="@+id/navigation_settings_extensions"
        android:name="com.lagradost.cloudstream3.ui.settings.extensions.ExtensionsFragment"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_extensions">
        <action
            android:id="@+id/navigation_settings_extensions_to_navigation_settings_plugins"
            app:destination="@id/navigation_settings_plugins"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim">
            <argument
                android:name="name"
                android:defaultValue="@null"
                app:argType="string" />
            <argument
                android:name="url"
                android:defaultValue="@null"
                app:argType="string" />
            <argument
                android:name="isLocal"
                android:defaultValue="false"
                app:argType="boolean" />
        </action>
    </fragment>

    <fragment
        android:id="@+id/navigation_settings_plugins"
        android:name="com.lagradost.cloudstream3.ui.settings.extensions.PluginsFragment"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_plugins" />

    <fragment
        android:id="@+id/navigation_webview"
        android:name="com.lagradost.cloudstream3.ui.WebviewFragment"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_webview" />

    <fragment
        android:id="@+id/navigation_settings_providers"
        android:name="com.lagradost.cloudstream3.ui.settings.SettingsProviders"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />

    <fragment
        android:id="@+id/navigation_settings_updates"
        android:name="com.lagradost.cloudstream3.ui.settings.SettingsUpdates"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />

    <fragment
        android:id="@+id/navigation_settings_account"
        android:name="com.lagradost.cloudstream3.ui.settings.SettingsAccount"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />

    <action
        android:id="@+id/global_to_navigation_quick_search"
        app:destination="@id/navigation_quick_search"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="mainapi"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="autosearch"
            android:defaultValue="@null"
            app:argType="string" />
    </action>

    <fragment
        android:id="@+id/navigation_home"
        android:name="com.lagradost.cloudstream3.ui.home.HomeFragment"
        android:label="@string/title_home"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_navigation_home_to_navigation_quick_search"
            app:destination="@id/navigation_quick_search"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_search"
        android:name="com.lagradost.cloudstream3.ui.search.SearchFragment"
        android:label="@string/title_search"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_search">
        <argument
            android:name="search_query"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/navigation_downloads"
        android:name="com.lagradost.cloudstream3.ui.download.DownloadFragment"
        android:label="@string/title_downloads"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_downloads">

        <action
            android:id="@+id/action_navigation_downloads_to_navigation_download_child"
            app:destination="@id/navigation_download_child"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim">
            <argument
                android:name="name"
                app:argType="string" />
            <argument
                android:name="folder"
                app:argType="string" />
        </action>
        <action
            android:id="@+id/action_navigation_downloads_to_navigation_player"
            app:destination="@id/navigation_player"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>
    <fragment
        android:id="@+id/navigation_settings"
        android:name="com.lagradost.cloudstream3.ui.settings.SettingsFragment"
        android:layout_height="match_parent"
        android:label="@string/title_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/main_settings" />
    <action
        android:id="@+id/action_navigation_global_to_navigation_settings_ui"
        app:destination="@id/navigation_settings_ui"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />
    <action
        android:id="@+id/action_navigation_global_to_navigation_settings_providers"
        app:destination="@id/navigation_settings_providers"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />
    <action
        android:id="@+id/action_navigation_global_to_navigation_settings_player"
        app:destination="@id/navigation_settings_player"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />
    <action
        android:id="@+id/action_navigation_global_to_navigation_settings_updates"
        app:destination="@id/navigation_settings_updates"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />
    <action
        android:id="@+id/action_navigation_global_to_navigation_settings_account"
        app:destination="@id/navigation_settings_account"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />
    <action
        android:id="@+id/action_navigation_global_to_navigation_settings_general"
        app:destination="@id/navigation_settings_general"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />
    <action
        android:id="@+id/action_navigation_global_to_navigation_settings_extensions"
        app:destination="@id/navigation_settings_extensions"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim" />

    <fragment
        android:id="@+id/navigation_subtitles"
        android:name="com.lagradost.cloudstream3.ui.subtitles.SubtitlesFragment"
        android:layout_height="match_parent"
        android:label="@string/subtitles_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/subtitle_settings" />

    <fragment
        android:id="@+id/navigation_chrome_subtitles"
        android:name="com.lagradost.cloudstream3.ui.subtitles.ChromecastSubtitlesFragment"
        android:layout_height="match_parent"
        android:label="@string/chromecast_subtitles_settings"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/chromecast_subtitle_settings" />

    <fragment
        android:id="@+id/navigation_quick_search"
        android:name="com.lagradost.cloudstream3.ui.quicksearch.QuickSearchFragment"
        android:layout_height="match_parent"
        android:label="@string/search"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/quick_search" />

    <fragment
        android:id="@+id/navigation_download_child"
        android:name="com.lagradost.cloudstream3.ui.download.DownloadChildFragment"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_child_downloads">
        <action
            android:id="@+id/action_navigation_download_child_to_navigation_player"
            app:destination="@id/navigation_player"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_results_phone"
        android:name="com.lagradost.cloudstream3.ui.result.ResultTrailerPlayer"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_result_swipe">
        <action
            android:id="@+id/action_navigation_results_phone_to_navigation_quick_search"
            app:destination="@id/navigation_quick_search"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
        <action
            android:id="@+id/action_navigation_results_phone_to_navigation_player"
            app:destination="@id/navigation_player"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_results_tv"
        android:name="com.lagradost.cloudstream3.ui.result.ResultFragmentTv"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_result_swipe">
        <action
            android:id="@+id/action_navigation_results_tv_to_navigation_quick_search"
            app:destination="@id/navigation_quick_search"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
        <action
            android:id="@+id/action_navigation_results_tv_to_navigation_player"
            app:destination="@id/navigation_player"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <!--<fragment
        android:id="@+id/navigation_results"
        android:name="com.lagradost.cloudstream3.ui.result.ResultFragment"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_result_swipe">
        <action
            android:id="@+id/action_navigation_results_to_navigation_quick_search"
            app:destination="@id/navigation_quick_search"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
        <action
            android:id="@+id/action_navigation_results_to_navigation_player"
            app:destination="@id/navigation_player"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>-->

    <fragment
        android:id="@+id/navigation_player"
        android:name="com.lagradost.cloudstream3.ui.player.GeneratorPlayer"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_player" />

    <fragment
        android:id="@+id/navigation_test_providers"
        android:name="com.lagradost.cloudstream3.ui.settings.testing.TestFragment"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_testing">
        <action
            android:id="@+id/action_navigation_global_to_navigation_test_providers"
            app:destination="@id/navigation_test_providers"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_setup_language"
        android:name="com.lagradost.cloudstream3.ui.setup.SetupFragmentLanguage"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_setup_language">
        <action
            android:id="@+id/action_navigation_setup_language_to_navigation_setup_provider_languages"
            app:destination="@id/navigation_setup_provider_languages"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <action
        android:id="@+id/action_navigation_global_to_navigation_setup_extensions"
        app:destination="@id/navigation_setup_extensions"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim">
        <argument
            android:name="isSetup"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>

    <fragment
        android:id="@+id/navigation_setup_extensions"
        android:name="com.lagradost.cloudstream3.ui.setup.SetupFragmentExtensions"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_setup_extensions">
        <action
            android:id="@+id/action_navigation_setup_extensions_to_navigation_setup_provider_languages"
            app:destination="@id/navigation_setup_provider_languages"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
        <action
            android:id="@+id/action_navigation_setup_extensions_to_navigation_setup_media"
            app:destination="@id/navigation_setup_media"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_setup_provider_languages"
        android:name="com.lagradost.cloudstream3.ui.setup.SetupFragmentProviderLanguage"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_setup_provider_languages">
        <action
            android:id="@+id/navigation_setup_provider_languages_to_navigation_setup_media"
            app:destination="@id/navigation_setup_media"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_setup_media"
        android:name="com.lagradost.cloudstream3.ui.setup.SetupFragmentMedia"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_setup_media">
        <action
            android:id="@+id/navigation_setup_media_to_navigation_setup_layout"
            app:destination="@id/navigation_setup_layout"
            app:enterAnim="@anim/enter_anim"
            app:exitAnim="@anim/exit_anim"
            app:popEnterAnim="@anim/enter_anim"
            app:popExitAnim="@anim/exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/navigation_setup_layout"
        android:name="com.lagradost.cloudstream3.ui.setup.SetupFragmentLayout"
        android:layout_height="match_parent"
        app:enterAnim="@anim/enter_anim"
        app:exitAnim="@anim/exit_anim"
        app:popEnterAnim="@anim/enter_anim"
        app:popExitAnim="@anim/exit_anim"
        tools:layout="@layout/fragment_setup_media" />

    <activity
        android:id="@+id/accountSelectActivity"
        android:name="com.lagradost.cloudstream3.ui.account.AccountSelectActivity"
        android:label="AccountSelectActivity"
        tools:layout="@layout/activity_account_select" />

    <fragment
        android:id="@+id/easterEggMonkeFragment"
        android:name="com.lagradost.cloudstream3.ui.EasterEggMonkeFragment"
        tools:layout="@layout/fragment_easter_egg_monke"
        android:label="EasterEggMonkeFragment" />
</navigation>
