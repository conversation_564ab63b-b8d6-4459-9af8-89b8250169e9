<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/episode_holder"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginEnd="10dp"
    android:foreground="@drawable/outline_drawable"
    android:focusable="true"
    android:nextFocusRight="@id/download_button"
    app:cardBackgroundColor="?attr/boxItemBackground"
    app:cardCornerRadius="@dimen/rounded_image_radius">

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/download_header_episode_progress"
        style="@android:style/Widget.Material.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="-1.5dp"
        android:progressBackgroundTint="?attr/colorPrimary"
        android:progressTint="?attr/colorPrimary"
        tools:progress="50" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?android:attr/selectableItemBackgroundBorderless"
        android:orientation="horizontal">
        <!--app:cardCornerRadius="@dimen/roundedImageRadius"-->
        <androidx.cardview.widget.CardView
            android:layout_width="70dp"
            android:layout_height="104dp">

            <ImageView
                android:id="@+id/download_header_poster"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/episode_poster_img_des"
                android:scaleType="centerCrop"
                tools:src="@drawable/example_poster" />
        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="70dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/download_header_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/textColor"
                android:textStyle="bold"
                tools:text="Perfect Run" />

            <TextView
                android:id="@+id/download_header_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/grayTextColor"
                tools:text="1 episode | 285MB" />
        </LinearLayout>

        <ImageView
            android:id="@+id/download_header_goto_child"
            android:layout_width="@dimen/download_size"
            android:layout_height="@dimen/download_size"
            android:layout_gravity="center_vertical|end"
            android:layout_marginStart="-50dp"
            android:contentDescription="@string/download"
            android:padding="10dp"
            android:src="@drawable/ic_baseline_keyboard_arrow_right_24" />

        <com.lagradost.cloudstream3.ui.download.button.PieFetchButton
            android:id="@+id/download_button"
            android:layout_width="@dimen/download_size"
            android:layout_height="@dimen/download_size"
            android:layout_gravity="center_vertical|end"
            android:layout_marginStart="-50dp"
            android:foreground="@drawable/outline_drawable"
            android:focusable="true"
            android:nextFocusLeft="@id/episode_holder"
            android:padding="10dp" />

        <CheckBox
            android:id="@+id/delete_checkbox"
            android:layout_width="@dimen/download_size"
            android:layout_height="@dimen/download_size"
            android:layout_gravity="center_vertical|end"
            android:layout_marginStart="-50dp"
            android:focusable="true"
            android:nextFocusLeft="@id/episode_holder"
            android:padding="10dp"
            android:visibility="gone" />
    </LinearLayout>
</androidx.cardview.widget.CardView>