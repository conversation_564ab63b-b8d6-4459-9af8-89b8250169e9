<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

    <androidx.cardview.widget.CardView xmlns:app="http://schemas.android.com/apk/res-auto"
            xmlns:tools="http://schemas.android.com/tools"
            android:foreground="@drawable/outline_drawable"
            android:layout_margin="2dp"
            android:layout_width="114dp"
            android:layout_height="180dp"
            android:layout_marginBottom="2dp"
            android:elevation="10dp"
            app:cardCornerRadius="@dimen/rounded_image_radius"
            android:id="@+id/background_card"
            app:cardBackgroundColor="?attr/primaryGrayBackground">

        <ImageView
                android:duplicateParentState="true"
                android:id="@+id/imageView"
                tools:src="@drawable/example_poster"
                android:scaleType="centerCrop"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/search_poster_img_des" />
    </androidx.cardview.widget.CardView>

    <ListView
            android:nextFocusRight="@id/cancel_btt"
            android:nextFocusLeft="@id/apply_btt"

            android:nextFocusUp="@id/toggle1"
            android:nextFocusDown="@id/apply_btt"

            android:id="@+id/listview1"
            android:requiresFadingEdge="vertical"
            tools:listitem="@layout/sort_bottom_single_choice_color"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_rowWeight="1" />
</LinearLayout>