<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <TextView
        android:id="@+id/home_child_more_info"
        style="@style/WatchHeaderText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="0dp"
        android:padding="12dp"
        tools:text="@string/continue_watching"
        app:drawableRightCompat="@drawable/ic_baseline_arrow_forward_24"
        app:drawableTint="?attr/white"
        android:background="?android:attr/selectableItemBackground"
        android:contentDescription="@string/home_more_info"/>

    <androidx.recyclerview.widget.RecyclerView
            android:nextFocusUp="@id/home_child_more_info"
            android:paddingHorizontal="5dp"
            android:clipToPadding="false"
            android:descendantFocusability="afterDescendants"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:id="@+id/home_child_recyclerview"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/home_result_grid" />
</LinearLayout>