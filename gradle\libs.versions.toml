[versions]
acraCore = "5.12.0"
appcompat = "1.7.0"
biometric = "1.4.0-alpha03"
buildkonfigGradlePlugin = "0.15.2"
coil = "3.1.0"
colorpicker = "6b46b49bd5"
conscryptAndroid = "2.5.2"
constraintlayout = "2.2.1"
coreKtx = "1.16.0"
desugar_jdk_libs_nio = "2.1.5"
dokkaGradlePlugin = "2.0.0"
espressoCore = "3.6.1"
fuzzywuzzy = "1.4.0"
gradle = "8.9.2"
jacksonModuleKotlin = "2.13.1"
json = "20250107"
junit = "4.13.2"
junitKtx = "1.2.1"
junitVersion = "1.2.1"
juniversalchardet = "2.5.0"
kotlinGradlePluginVersion = "2.1.10"
kotlinxCoroutinesCore = "1.10.1"
lifecycleLivedataKtx = "2.8.7"
lifecycleViewmodelKtx = "2.8.7"
material = "1.12.0"
media3 = "1.6.1"
navigationKtx = "2.8.9"
newpipeextractor = "v0.24.6"
nextlibMedia3 = "0.8.4"
nicehttp = "0.4.13"
overlappingpanels = "0.1.5"
paletteKtx = "1.0.0"
preferenceKtx = "1.2.1"
previewseekbarMedia3 = "*******"
qrcodeKotlin = "4.3.0"
rhino = "1.8.0"
safefile = "0.0.8"
shimmer = "0.5.0"
swiperefreshlayout = "1.1.0"
tmdbJava = "2.11.0"
torrentserver = "7861970e038b35cd8c6918384e49caf26903e09e"
tvprovider = "1.0.0"
video = "1.0.0"
workRuntime = "2.10.0"
workRuntimeKtx = "2.10.0"

jvmTarget = "1.8"
minSdk = "21"
compileSdk = "35"
targetSdk = "35"

[libraries]
acra-core = { module = "ch.acra:acra-core", version.ref = "acraCore" }
acra-toast = { module = "ch.acra:acra-toast", version.ref = "acraCore" }
appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
biometric = { module = "androidx.biometric:biometric", version.ref = "biometric" }
buildkonfig-gradle-plugin = { module = "com.codingfeline.buildkonfig:buildkonfig-gradle-plugin", version.ref = "buildkonfigGradlePlugin" }
coil = { module = "io.coil-kt.coil3:coil", version.ref = "coil" }
coil-network-okhttp = { module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coil" }
colorpicker = { module = "com.github.recloudstream:color-picker-android", version.ref = "colorpicker" }
conscrypt-android = { module = "org.conscrypt:conscrypt-android", version.ref = "conscryptAndroid" }
constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
core = { module = "androidx.test:core" }
core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
desugar_jdk_libs_nio = { module = "com.android.tools:desugar_jdk_libs_nio", version.ref = "desugar_jdk_libs_nio" }
dokka-gradle-plugin = { module = "org.jetbrains.dokka:dokka-gradle-plugin", version.ref = "dokkaGradlePlugin" }
espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCore" }
ext-junit = { module = "androidx.test.ext:junit", version.ref = "junitVersion" }
fuzzywuzzy = { module = "me.xdrop:fuzzywuzzy", version.ref = "fuzzywuzzy" }
gradle = { module = "com.android.tools.build:gradle", version.ref = "gradle" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jacksonModuleKotlin" }
jetbrains-kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlinGradlePluginVersion" }
json = { module = "org.json:json", version.ref = "json" }
junit = { module = "junit:junit", version.ref = "junit" }
junit-ktx = { module = "androidx.test.ext:junit-ktx", version.ref = "junitKtx" }
juniversalchardet = { module = "com.github.albfernandez:juniversalchardet", version.ref = "juniversalchardet" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
material = { module = "com.google.android.material:material", version.ref = "material" }
media3-cast = { module = "androidx.media3:media3-cast", version.ref = "media3" }
media3-common = { module = "androidx.media3:media3-common", version.ref = "media3" }
media3-datasource-okhttp = { module = "androidx.media3:media3-datasource-okhttp", version.ref = "media3" }
media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3" }
media3-exoplayer-dash = { module = "androidx.media3:media3-exoplayer-dash", version.ref = "media3" }
media3-exoplayer-hls = { module = "androidx.media3:media3-exoplayer-hls", version.ref = "media3" }
media3-session = { module = "androidx.media3:media3-session", version.ref = "media3" }
media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3" }
navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigationKtx" }
navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigationKtx" }
newpipeextractor = { module = "com.github.teamnewpipe:NewPipeExtractor", version.ref = "newpipeextractor" }
nextlib-media3ext = { module = "com.github.anilbeesetti.nextlib:nextlib-media3ext", version.ref = "nextlibMedia3" }
nextlib-mediainfo = { module = "com.github.anilbeesetti.nextlib:nextlib-mediainfo", version.ref = "nextlibMedia3" }
nicehttp = { module = "com.github.Blatzar:NiceHttp", version.ref = "nicehttp" }
overlappingpanels = { module = "com.github.discord:OverlappingPanels", version.ref = "overlappingpanels" }
palette-ktx = { module = "androidx.palette:palette-ktx", version.ref = "paletteKtx" }
preference-ktx = { module = "androidx.preference:preference-ktx", version.ref = "preferenceKtx" }
previewseekbar-media3 = { module = "com.github.rubensousa:previewseekbar-media3", version.ref = "previewseekbarMedia3" }
qrcode-kotlin = { module = "io.github.g0dkar:qrcode-kotlin", version.ref = "qrcodeKotlin" }
rhino = { module = "org.mozilla:rhino", version.ref = "rhino" }
quickjs = { module = "app.cash.quickjs:quickjs-android", version = "0.9.2" }
safefile = { module = "com.github.LagradOst:SafeFile", version.ref = "safefile" }
shimmer = { module = "com.facebook.shimmer:shimmer", version.ref = "shimmer" }
swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version.ref = "swiperefreshlayout" }
tmdb-java = { module = "com.uwetrottmann.tmdb2:tmdb-java", version.ref = "tmdbJava" }
torrentserver = { module = "com.github.recloudstream:torrentserver", version.ref = "torrentserver" }
tvprovider = { module = "androidx.tvprovider:tvprovider", version.ref = "tvprovider" }
video = { module = "com.google.android.mediahome:video", version.ref = "video" }
work-runtime = { module = "androidx.work:work-runtime", version.ref = "workRuntime" }
work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "workRuntimeKtx" }

[plugins]

[bundles]
media3 = ["media3-cast", "media3-common", "media3-datasource-okhttp", "media3-exoplayer", "media3-exoplayer-dash", "media3-exoplayer-hls", "media3-session", "media3-ui"]
nextlibMedia3 = ["nextlib-media3ext", "nextlib-mediainfo"]
navigationKtx = ["navigation-ui-ktx", "navigation-fragment-ktx"]
