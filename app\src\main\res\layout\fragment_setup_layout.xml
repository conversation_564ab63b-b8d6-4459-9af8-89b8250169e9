<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setup_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:orientation="horizontal">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginHorizontal="50dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/crash_reporting_title"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/crash_reporting_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/bug_report_settings_off" />
            </LinearLayout>


            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/acra_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:checked="true">

            </androidx.appcompat.widget.SwitchCompat>
        </FrameLayout>-->

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="@string/app_layout"
        android:textSize="18sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/app_layout_subtext">

    </TextView>

    <ListView
        android:id="@+id/listview1"
        android:layout_width="match_parent"

        android:layout_height="match_parent"
        android:layout_rowWeight="1"
        android:layout_marginBottom="60dp"
        android:nextFocusLeft="@id/apply_btt"
        android:nextFocusRight="@id/cancel_btt"
        android:paddingTop="10dp"
        android:requiresFadingEdge="vertical"
        tools:listitem="@layout/sort_bottom_single_choice" />

    <LinearLayout
        android:id="@+id/apply_btt_holder"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:layout_marginTop="-60dp"
        android:gravity="bottom|end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/next_btt"
            style="@style/WhiteButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/setup_done" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/prev_btt"
            style="@style/BlackButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/previous" />
    </LinearLayout>


</LinearLayout>