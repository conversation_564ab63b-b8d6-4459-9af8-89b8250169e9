<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/settings_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/primaryGrayBackground"
        android:descendantFocusability="afterDescendants"
        android:paddingTop="@dimen/navbar_height"
        app:layout_scrollFlags="scroll|enterAlways"
        app:navigationIconTint="?attr/iconColor"
        app:titleTextColor="?attr/textColor"
        tools:title="Overlord" />
</com.google.android.material.appbar.AppBarLayout>