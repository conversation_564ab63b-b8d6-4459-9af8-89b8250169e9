<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <SwitchPreference
        android:icon="@drawable/baseline_sync_24"
        android:summary="@string/episode_sync_settings_des"
        android:title="@string/episode_sync_settings"
        app:defaultValue="true"
        app:key="@string/episode_sync_enabled_key" />

    <PreferenceCategory android:title="@string/pref_category_defaults">
        <!--        <Preference-->
        <!--            android:icon="@drawable/ic_baseline_hd_24"-->
        <!--            android:key="@string/quality_pref_key"-->
        <!--            android:title="@string/watch_quality_pref" />-->
        <!--        <Preference-->
        <!--            android:icon="@drawable/ic_baseline_hd_24"-->
        <!--            android:key="@string/quality_pref_mobile_data_key"-->
        <!--            android:title="@string/watch_quality_pref_data" />-->

        <Preference
            android:icon="@drawable/netflix_play"
            android:key="@string/player_default_key"
            android:title="@string/player_pref" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_category_player_layout">
        <Preference
            android:icon="@drawable/ic_baseline_text_format_24"
            android:key="@string/prefer_limit_title_key"
            android:title="@string/limit_title" />

        <Preference
            android:icon="@drawable/ic_baseline_text_format_24"
            android:key="@string/prefer_limit_title_rez_key"
            android:title="@string/limit_title_rez" />
        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_baseline_text_format_24"
            android:key="@string/hide_player_control_names_key"
            android:title="@string/hide_player_control_names" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_category_subtitles">
        <Preference
            android:icon="@drawable/ic_outline_subtitles_24"
            android:key="@string/subtitle_settings_key"
            android:title="@string/player_subtitles_settings"
            app:summary="@string/player_subtitles_settings_des" />
        <Preference
            android:icon="@drawable/ic_outline_subtitles_24"
            android:key="@string/subtitle_settings_chromecast_key"
            android:title="@string/chromecast_subtitles_settings"
            app:summary="@string/chromecast_subtitles_settings_des" />
    </PreferenceCategory>


    <PreferenceCategory android:title="@string/pref_category_player_features">
        <SwitchPreference
            android:icon="@drawable/ic_baseline_picture_in_picture_alt_24"
            android:summary="@string/picture_in_picture_des"
            android:title="@string/picture_in_picture"
            app:defaultValue="true"
            app:key="@string/pip_enabled_key" />
        <SwitchPreference
            android:icon="@drawable/ic_baseline_aspect_ratio_24"
            android:summary="@string/player_size_settings_des"
            android:title="@string/player_size_settings"
            app:defaultValue="true"
            app:key="@string/player_resize_enabled_key" />
        <SwitchPreference
            android:icon="@drawable/ic_baseline_speed_24"
            android:summary="@string/speed_setting_summary"
            android:title="@string/eigengraumode_settings"
            app:defaultValue="false"
            app:key="@string/playback_speed_enabled_key" />
        <SwitchPreference
            android:icon="@drawable/speedup"
            android:summary="@string/speedup_summary"
            android:title="@string/speedup_title"
            app:defaultValue="false"
            app:key="@string/speedup_key" />
        <SwitchPreference
            android:icon="@drawable/ic_baseline_skip_next_24"
            android:summary="@string/autoplay_next_settings_des"
            android:title="@string/autoplay_next_settings"
            app:defaultValue="true"
            app:key="@string/autoplay_next_key" />
        <SwitchPreference
            android:icon="@drawable/ic_baseline_skip_next_24"
            android:summary="@string/enable_skip_op_from_database_des"
            android:title="@string/video_skip_op"
            app:defaultValue="true"
            app:key="@string/enable_skip_op_from_database" />
        <SwitchPreference
            android:icon="@drawable/screen_rotation"
            android:summary="@string/rotate_video_desc"
            android:title="@string/rotate_video"
            app:defaultValue="false"
            app:key="@string/rotate_video_key" />
        <SwitchPreference
            android:icon="@drawable/screen_rotation"
            android:summary="@string/auto_rotate_video_desc"
            android:title="@string/auto_rotate_video"
            app:defaultValue="false"
            app:key="@string/auto_rotate_video_key" />
        <SwitchPreference
            android:icon="@drawable/preview_seekbar_24"
            android:summary="@string/preview_seekbar_desc"
            android:title="@string/preview_seekbar"
            app:defaultValue="true"
            app:key="@string/preview_seekbar_key" />
        <Preference
            android:icon="@drawable/ic_baseline_extension_24"
            android:summary="@string/software_decoding_desc"
            android:title="@string/software_decoding"
            app:defaultValue="true"
            app:key="@string/software_decoding_key" />
    </PreferenceCategory>
    <PreferenceCategory
        android:title="@string/pref_category_gestures"
        app:key="@string/pref_category_gestures_key">
        <SwitchPreference
            android:icon="@drawable/ic_baseline_ondemand_video_24"
            android:summary="@string/swipe_to_seek_settings_des"
            android:title="@string/swipe_to_seek_settings"
            app:defaultValue="true"
            app:key="@string/swipe_enabled_key" />
        <SwitchPreference
            android:icon="@drawable/ic_baseline_ondemand_video_24"
            android:summary="@string/swipe_to_change_settings_des"
            android:title="@string/swipe_to_change_settings"
            app:defaultValue="true"
            app:key="@string/swipe_vertical_enabled_key" />
        <SwitchPreference
            android:icon="@drawable/ic_baseline_touch_app_24"
            android:summary="@string/double_tap_to_seek_settings_des"
            android:title="@string/double_tap_to_seek_settings"
            app:defaultValue="false"
            app:key="@string/double_tap_enabled_key" />
        <SwitchPreference
            android:icon="@drawable/netflix_pause"
            android:summary="@string/double_tap_to_pause_settings_des"
            android:title="@string/double_tap_to_pause_settings"
            app:defaultValue="false"
            app:key="@string/double_tap_pause_enabled_key" />

        <SeekBarPreference
            android:defaultValue="10"
            android:max="60"
            android:title="@string/double_tap_to_seek_amount_settings"
            app:adjustable="true"
            app:defaultValue="10"
            app:icon="@drawable/go_forward_30"
            app:key="@string/double_tap_seek_time_key"
            app:min="5"
            app:seekBarIncrement="5"
            app:showSeekBarValue="true" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_category_cache">
        <Preference
            android:icon="@drawable/ic_baseline_storage_24"
            android:key="@string/video_buffer_disk_key"
            android:summary="@string/video_disk_description"
            android:title="@string/video_buffer_disk_settings" />

        <Preference
            android:icon="@drawable/ic_baseline_storage_24"
            android:key="@string/video_buffer_size_key"
            android:summary="@string/video_ram_description"
            android:title="@string/video_buffer_size_settings" />

        <Preference
            android:icon="@drawable/ic_baseline_storage_24"
            android:key="@string/video_buffer_length_key"
            android:summary="@string/video_ram_description"
            android:title="@string/video_buffer_length_settings" />

        <Preference
            android:icon="@drawable/ic_baseline_delete_outline_24"
            android:key="@string/video_buffer_clear_key"
            android:title="@string/video_buffer_clear_settings" />
    </PreferenceCategory>
    <PreferenceCategory
        android:key="@string/pref_category_android_tv_key"
        android:title="@string/pref_category_android_tv">
        <SeekBarPreference
            android:defaultValue="10"
            android:max="60"
            android:summary="@string/android_tv_interface_on_seek_settings_summary"
            android:title="@string/android_tv_interface_on_seek_settings"
            app:adjustable="true"
            app:defaultValue="30"
            app:icon="@drawable/go_forward_30"
            app:key="@string/android_tv_interface_on_seek_key"
            app:min="5"
            app:seekBarIncrement="5"
            app:showSeekBarValue="true" />
        <SeekBarPreference
            android:defaultValue="10"
            android:max="60"
            android:summary="@string/android_tv_interface_off_seek_settings_summary"
            android:title="@string/android_tv_interface_off_seek_settings"
            app:adjustable="true"
            app:defaultValue="10"
            app:icon="@drawable/go_forward_30"
            app:key="@string/android_tv_interface_off_seek_key"
            app:min="5"
            app:seekBarIncrement="5"
            app:showSeekBarValue="true" />
    </PreferenceCategory>
</PreferenceScreen>