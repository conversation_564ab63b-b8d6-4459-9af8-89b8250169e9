<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/result_root"
    style="@style/DarkFragment"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/primaryBlackBackground"
    android:clickable="true"
    android:focusable="true">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/result_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:orientation="vertical"
        app:shimmer_auto_start="true"
        app:shimmer_base_alpha="0.2"
        app:shimmer_duration="@integer/loading_time"
        app:shimmer_highlight_alpha="0.3"
        tools:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/result_padding"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/loading_margin"
                android:orientation="horizontal">

                <include layout="@layout/loading_poster" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/loading_margin"
                    android:layout_marginEnd="@dimen/loading_margin"
                    android:orientation="vertical">

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line_short" />
                </LinearLayout>
            </LinearLayout>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="20dp"
                tools:ignore="ContentDescription" />

            <include layout="@layout/loading_episode" />

            <include layout="@layout/loading_episode" />

            <include layout="@layout/loading_episode" />
        </LinearLayout>
    </com.facebook.shimmer.ShimmerFrameLayout>
    <!--<ProgressBar
            android:visibility="visible"
            tools:visibility="gone"
            android:id="@+id/result_loading"
            android:layout_gravity="center"
            android:layout_width="50dp" android:layout_height="50dp">
    </ProgressBar>-->
    <LinearLayout
        android:id="@+id/result_loading_error"

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="gone">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/result_reload_connectionerror"
            style="@style/WhiteButton"

            android:layout_width="wrap_content"

            android:layout_gravity="center"
            android:layout_margin="5dp"
            android:minWidth="200dp"
            android:text="@string/reload_error"
            app:icon="@drawable/ic_baseline_autorenew_24" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/result_reload_connection_open_in_browser"
            style="@style/BlackButton"

            android:layout_width="wrap_content"

            android:layout_gravity="center"
            android:layout_margin="5dp"
            android:minWidth="200dp"
            android:text="@string/result_open_in_browser"
            app:icon="@drawable/ic_baseline_public_24" />

        <TextView
            android:id="@+id/result_error_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="5dp"
            android:gravity="center"
            android:textColor="?attr/textColor" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/result_finish_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/result_scroll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:paddingBottom="100dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?attr/primaryBlackBackground"
                android:orientation="vertical">

                <!--
                <com.facebook.shimmer.ShimmerFrameLayout
                    android:id="@+id/result_trailer_loading"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:shimmer_auto_start="true"
                    app:shimmer_base_alpha="0.2"
                    app:shimmer_duration="@integer/loading_time"
                    app:shimmer_highlight_alpha="0.3"
                    tools:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/result_padding"
                        android:orientation="vertical">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="150dp"
                            android:background="@color/grayShimmer"
                            android:foreground="@drawable/outline_drawable"
                            app:cardCornerRadius="@dimen/loading_radius" />
                    </LinearLayout>
                </com.facebook.shimmer.ShimmerFrameLayout>
-->


                <!--
                <FrameLayout
                        android:background="?attr/primaryGrayBackground"
                        android:paddingStart="@dimen/result_padding"
                        android:paddingEnd="@dimen/result_padding"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                    <ImageView
                            android:nextFocusDown="@id/result_bookmark_button"
                            android:nextFocusRight="@id/result_share"
                            android:background="?android:attr/selectableItemBackgroundBorderless"

                            android:id="@+id/result_back"
                            android:clickable="true"
                            android:focusable="true"

                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_gravity="center_vertical|start"
                            android:src="@drawable/ic_baseline_arrow_back_24"
                            android:contentDescription="@string/go_back"
                            app:tint="?attr/white" />

                    <LinearLayout
                            android:gravity="end"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:id="@+id/media_route_button_holder"
                            android:layout_gravity="center_vertical|end">

                        <androidx.mediarouter.app.MediaRouteButton
                                android:layout_gravity="end|center_vertical"
                                android:id="@+id/media_route_button"
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:mediaRouteTypes="user"
                                android:visibility="gone"
                                app:mediaRouteButtonTint="?attr/textColor" />


                        <ImageView
                                android:nextFocusUp="@id/result_back"
                                android:nextFocusDown="@id/result_descript"
                                android:nextFocusLeft="@id/result_add_sync"
                                android:nextFocusRight="@id/result_openinbrower"

                                android:id="@+id/result_share"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_marginEnd="10dp"
                                android:elevation="10dp"

                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:src="@drawable/ic_outline_share_24"
                                android:layout_gravity="end|center_vertical"
                                android:contentDescription="@string/result_share"
                                app:tint="?attr/textColor" />

                        <ImageView
                                android:nextFocusUp="@id/result_back"
                                android:nextFocusDown="@id/result_descript"
                                android:nextFocusLeft="@id/result_share"
                                android:nextFocusRight="@id/result_search"

                                android:id="@+id/result_openinbrower"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_margin="5dp"
                                android:elevation="10dp"

                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:src="@drawable/ic_baseline_public_24"
                                android:layout_gravity="end|center_vertical"
                                android:contentDescription="@string/result_open_in_browser"
                                app:tint="?attr/textColor" />

                        <ImageView
                                android:nextFocusUp="@id/result_back"
                                android:nextFocusDown="@id/result_descript"
                                android:nextFocusLeft="@id/result_openinbrower"
                                android:nextFocusRight="@id/result_bookmark_button"

                                android:id="@+id/result_search"
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:layout_margin="5dp"
                                android:elevation="10dp"

                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:src="@drawable/search_icon"
                                android:layout_gravity="end|center_vertical"
                                android:contentDescription="@string/result_open_in_browser"
                                app:tint="?attr/textColor" />
                    </LinearLayout>
                </FrameLayout>-->
                <FrameLayout
                    android:id="@+id/result_top_holder"
                    android:layout_width="match_parent"
                    android:layout_height="200dp">

                    <FrameLayout
                        android:id="@+id/result_smallscreen_holder"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:descendantFocusability="blocksDescendants">

                        <include
                            android:id="@+id/fragment_trailer"
                            layout="@layout/fragment_trailer" />
                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/result_poster_background_holder"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <ImageView
                            android:id="@+id/result_poster_background"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="60dp"
                            android:layout_gravity="bottom"
                            android:background="@drawable/background_shadow" />
                    </FrameLayout>
                </FrameLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/result_padding"
                    android:paddingEnd="@dimen/result_padding">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="15dp"
                        android:orientation="horizontal"
                        android:visibility="visible">


                        <androidx.cardview.widget.CardView
                            android:id="@+id/result_poster_holder"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:visibility="gone"
                            app:cardCornerRadius="@dimen/rounded_image_radius">

                            <ImageView
                                android:id="@+id/result_poster"
                                android:layout_width="100dp"
                                android:layout_height="140dp"
                                android:layout_gravity="bottom"
                                android:contentDescription="@string/result_poster_img_des"
                                android:foreground="@drawable/outline_drawable"
                                android:scaleType="centerCrop"
                                tools:src="@drawable/example_poster" />
                        </androidx.cardview.widget.CardView>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/result_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="5dp"
                                android:maxLines="2"
                                android:textColor="?attr/textColor"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                tools:text="The Perfect Run The Perfect Run" />

                            <com.lagradost.cloudstream3.widget.FlowLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:itemSpacing="10dp">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/result_meta_site"
                                    style="@style/SmallBlackButton"
                                    android:layout_gravity="center_vertical"
                                    tools:text="Gogoanime" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/result_meta_content_rating"
                                    style="@style/SmallBlackButton"
                                    android:layout_gravity="center_vertical"
                                    tools:text="PG-13" />

                                <TextView
                                    android:id="@+id/result_meta_type"
                                    style="@style/ResultInfoText"
                                    tools:text="Movie" />

                                <TextView
                                    android:id="@+id/result_meta_year"
                                    style="@style/ResultInfoText"
                                    tools:text="2022" />

                                <TextView
                                    android:id="@+id/result_meta_rating"
                                    style="@style/ResultInfoText"
                                    tools:text="Rated: 8.5/10.0" />

                                <TextView
                                    android:id="@+id/result_meta_status"
                                    style="@style/ResultInfoText"
                                    tools:text="Ongoing" />

                                <TextView
                                    android:id="@+id/result_meta_duration"
                                    style="@style/ResultInfoText"
                                    tools:text="121min" />
                            </com.lagradost.cloudstream3.widget.FlowLayout>

                            <!--
                            This has half margin and half padding to make TV focus on description look better.
                            The focus outline now settles between the poster and text.
                            -->
                            <FrameLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <TextView
                                    android:id="@+id/result_description"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:ellipsize="end"
                                    android:foreground="@drawable/outline_drawable"
                                    android:maxLines="10"
                                    android:nextFocusUp="@id/result_back"
                                    android:nextFocusDown="@id/result_bookmark_Button"
                                    android:paddingTop="5dp"
                                    android:textColor="?attr/textColor"
                                    android:textSize="15sp"
                                    tools:text="Ryan Quicksave Romano is an eccentric adventurer with a strange power: he can create a save-point in time and redo his life whenever he dies. Arriving in New Rome, the glitzy capital of sin of a rebuilding Europe, he finds the city torn between mega-corporations, sponsored heroes, superpowered criminals, and true monsters. It's a time of chaos, where potions can grant the power to rule the world and dangers lurk everywhere. " />

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="30dp"
                                    android:layout_gravity="bottom"
                                    android:src="@drawable/background_shadow"
                                    android:visibility="gone"
                                    tools:ignore="ContentDescription" />
                            </FrameLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <!--
                                        <com.google.android.material.button.MaterialButton
                                                android:id="@+id/result_bookmark_button"
                                                style="@style/BlackButton"
                                                android:layout_width="wrap_content"
                                                android:layout_gravity="center_vertical"
                                                android:layout_marginStart="0dp"
                                                android:layout_marginTop="0dp"

                                                android:layout_marginBottom="10dp"
                                                android:backgroundTint="?attr/primaryBlackBackground"

                                                android:minWidth="100dp"
                                                android:nextFocusLeft="@id/result_back"
                                                android:nextFocusRight="@id/result_search"
                                                android:nextFocusUp="@id/result_description"

                                                android:nextFocusDown="@id/result_cast_items"
                                                android:paddingTop="0dp"
                                                app:cornerRadius="4dp"
                                                app:icon="@drawable/ic_baseline_bookmark_24"
                                                tools:text="Bookmark"
                                                tools:visibility="visible" />
                    -->

                    <TextView
                        android:id="@+id/result_cast_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:textColor="?attr/grayTextColor"
                        android:textSize="15sp"
                        tools:text="Cast: Joe Ligma" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/result_cast_items"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"

                        android:descendantFocusability="afterDescendants"
                        android:fadingEdge="horizontal"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:nextFocusUp="@id/result_bookmark_Button"
                        android:nextFocusDown="@id/result_play_movie"
                        android:orientation="horizontal"
                        android:paddingTop="5dp"
                        android:requiresFadingEdge="horizontal"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/cast_item"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/result_vpn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="?attr/grayTextColor"
                        android:textSize="15sp"
                        tools:text="@string/vpn_torrent" />

                    <TextView
                        android:id="@+id/result_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:textColor="?attr/grayTextColor"
                        android:textSize="15sp"
                        tools:text="@string/provider_info_meta" />

                    <TextView
                        android:id="@+id/result_no_episodes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:textColor="?attr/grayTextColor"
                        android:textSize="15sp"
                        tools:text="@string/no_episodes_found" />

                    <TextView
                        android:id="@+id/result_tag_holder"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:text="@string/result_tags"
                        android:textColor="?attr/textColor"
                        android:textSize="17sp"
                        android:textStyle="normal"
                        android:visibility="gone" />

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/result_tag"
                        style="@style/ChipParent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                    <!--<com.lagradost.cloudstream3.widget.FlowLayout
                            android:id="@+id/result_tag"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />-->

                    <TextView
                        android:id="@+id/result_coming_soon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:paddingTop="50dp"
                        android:text="@string/coming_soon"
                        android:textColor="?attr/textColor"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/result_data_holder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/result_add_sync"
                            style="@style/WhiteButton"
                            android:layout_width="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="0dp"

                            android:layout_marginBottom="10dp"
                            android:text="@string/add_sync"
                            android:visibility="gone"
                            app:icon="@drawable/ic_baseline_add_24" />

                        <LinearLayout
                            android:id="@+id/result_movie_parent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:orientation="vertical"
                            tools:visibility="visible">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/result_play_movie"
                                style="@style/WhiteButton"
                                android:layout_width="match_parent"

                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="0dp"
                                android:layout_marginEnd="0dp"
                                android:layout_marginBottom="10dp"
                                android:nextFocusUp="@id/result_bookmark_Button"
                                android:nextFocusDown="@id/result_download_movie"
                                android:text="@string/play_movie_button"
                                android:visibility="visible"
                                app:icon="@drawable/ic_baseline_play_arrow_24">

                                <requestFocus />

                            </com.google.android.material.button.MaterialButton>


                            <!--<com.google.android.material.button.MaterialButton
                                    android:nextFocusUp="@id/result_play_movie"
                                    android:nextFocusDown="@id/result_season_button"
                                    android:layout_marginBottom="10dp"

                                    android:id="@+id/result_download_movie"
                                    style="@style/BlackButton"
                                    android:layout_marginStart="0dp"
                                    android:layout_marginEnd="0dp"
                                    android:visibility="visible"
                                    android:layout_gravity="center_vertical"
                                    tools:text="Downloading"
                                    tools:icon="@drawable/netflix_download"

                                    android:clickable="true"
                                    android:focusable="true"
                                    android:layout_width="match_parent" />-->

                            <com.lagradost.cloudstream3.ui.download.button.DownloadButton
                                android:id="@+id/download_button"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:download_layout="@layout/download_button_layout" />

                            <!--<androidx.core.widget.ContentLoadingProgressBar
                                    android:layout_width="match_parent"
                                    android:layout_height="20dp"
                                    tools:progress="50"
                                    android:id="@+id/result_movie_progress_downloaded"
                                    android:indeterminate="false"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:progressBackgroundTint="?attr/colorPrimary"
                                    android:max="100"
                                    android:layout_gravity="end|center_vertical"
                                    android:progress="0"
                                    android:visibility="gone"
                                    tools:visibility="visible" />
    -->
                            <!--  <TextView
                                      android:id="@+id/result_movie_text_progress"
                                      android:layout_gravity="center_vertical"
                                      android:gravity="center_vertical"
                                      tools:text="128MB / 237MB"
                                      android:textColor="?attr/grayTextColor"
                                      android:layout_width="wrap_content"
                                      android:layout_height="match_parent" />-->
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/result_resume_parent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="visible">


                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/result_resume_series_button"
                                style="@style/WhiteButton"
                                android:layout_width="match_parent"

                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="0dp"
                                android:layout_marginEnd="0dp"
                                android:layout_marginBottom="10dp"
                                android:nextFocusUp="@id/result_bookmark_Button"
                                android:nextFocusDown="@id/result_download_movie"
                                android:text="@string/resume"
                                android:visibility="visible"
                                app:icon="@drawable/ic_baseline_play_arrow_24"
                                tools:visibility="visible" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/result_next_series_button"
                                style="@style/WhiteButton"
                                android:layout_width="match_parent"

                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="0dp"
                                android:layout_marginEnd="0dp"
                                android:layout_marginBottom="10dp"
                                android:nextFocusUp="@id/result_bookmark_Button"
                                android:nextFocusDown="@id/result_download_movie"
                                android:text="@string/next_episode"
                                android:visibility="gone"
                                app:icon="@drawable/cast_ic_mini_controller_skip_next" />

                            <TextView
                                android:id="@+id/result_resume_series_title"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="?attr/textColor"
                                android:textSize="17sp"
                                android:textStyle="bold"
                                tools:text="S1E1 Episode 1" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/result_resume_progress_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="10dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <androidx.core.widget.ContentLoadingProgressBar
                                android:id="@+id/result_resume_series_progress"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="match_parent"
                                android:layout_height="20dp"
                                android:layout_gravity="end|center_vertical"
                                android:layout_weight="1"
                                android:indeterminate="false"
                                android:max="100"
                                android:progress="0"
                                android:progressBackgroundTint="?attr/colorPrimary"
                                android:visibility="visible"
                                tools:progress="50"
                                tools:visibility="visible" />

                            <TextView
                                android:id="@+id/result_resume_series_progress_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_weight="0"
                                android:gravity="center"
                                android:paddingStart="10dp"
                                android:textColor="?attr/grayTextColor"
                                tools:ignore="RtlSymmetry"
                                tools:text="69m\nremaining" />
                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <com.lagradost.cloudstream3.widget.FlowLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="10dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                app:itemSpacing="10dp">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/result_season_button"
                                    style="@style/MultiSelectButton"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_down_24"
                                    android:nextFocusLeft="@id/result_episode_select"
                                    android:nextFocusRight="@id/result_episode_select"
                                    android:nextFocusUp="@id/result_description"
                                    android:nextFocusDown="@id/result_episodes"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="5dp"
                                    android:visibility="gone"
                                    tools:text="Season 1"
                                    tools:visibility="visible" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/result_episode_select"
                                    style="@style/MultiSelectButton"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_down_24"
                                    android:nextFocusLeft="@id/result_season_button"
                                    android:nextFocusRight="@id/result_season_button"
                                    android:nextFocusUp="@id/result_description"
                                    android:nextFocusDown="@id/result_episodes"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="5dp"
                                    android:visibility="gone"
                                    tools:text="50-100"
                                    tools:visibility="visible" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/result_dub_select"
                                    style="@style/MultiSelectButton"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_down_24"
                                    android:nextFocusLeft="@id/result_season_button"
                                    android:nextFocusRight="@id/result_season_button"
                                    android:nextFocusUp="@id/result_description"
                                    android:nextFocusDown="@id/result_episodes"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="5dp"
                                    android:visibility="gone"
                                    tools:text="Dubbed1"
                                    tools:visibility="visible" />


                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/result_sort_button"
                                    style="@style/MultiSelectButton"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:nextFocusLeft="@id/result_dub_select"
                                    android:nextFocusUp="@id/result_description"
                                    android:nextFocusDown="@id/result_episodes"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="5dp"
                                    android:text="Sort"
                                    android:visibility="gone"
                                    tools:visibility="visible" />

                                <TextView
                                    android:id="@+id/result_episodes_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:paddingTop="10dp"
                                    android:paddingBottom="10dp"
                                    android:textColor="?attr/textColor"
                                    android:textSize="17sp"
                                    android:textStyle="normal"
                                    tools:text="8 Episodes" />
                            </com.lagradost.cloudstream3.widget.FlowLayout>


                            <!--TODO add next airing-->
                            <LinearLayout
                                android:id="@+id/result_next_airing_holder"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/result_next_airing"

                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:textColor="?attr/grayTextColor"
                                    android:textSize="17sp"
                                    android:textStyle="normal"
                                    tools:text="Episode 1022 will be released in" />

                                <TextView
                                    android:id="@+id/result_next_airing_time"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:paddingStart="5dp"
                                    android:paddingEnd="5dp"
                                    android:textColor="?attr/textColor"
                                    android:textSize="17sp"
                                    android:textStyle="normal"
                                    tools:text="5d 3h 30m" />
                            </LinearLayout>

                            <com.facebook.shimmer.ShimmerFrameLayout
                                android:id="@+id/result_episode_loading"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:layout_marginTop="15dp"
                                android:orientation="vertical"
                                app:shimmer_auto_start="true"
                                app:shimmer_base_alpha="0.2"
                                app:shimmer_duration="@integer/loading_time"
                                app:shimmer_highlight_alpha="0.3"
                                tools:visibility="visible">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical">

                                    <include layout="@layout/loading_episode" />

                                    <include layout="@layout/loading_episode" />

                                    <include layout="@layout/loading_episode" />

                                    <include layout="@layout/loading_episode" />
                                </LinearLayout>
                            </com.facebook.shimmer.ShimmerFrameLayout>
                            <!--<androidx.core.widget.ContentLoadingProgressBar
                                    android:id="@+id/result_episode_loading"

                                    style="@style/Widget.AppCompat.ProgressBar"
                                    android:layout_gravity="center"
                                    android:layout_width="50dp"
                                    android:layout_height="50dp" />-->

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/result_episodes"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="0dp"
                                android:clipToPadding="false"
                                android:descendantFocusability="afterDescendants"
                                android:paddingBottom="100dp"
                                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                tools:listitem="@layout/result_episode" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>


    <!--
    <androidx.cardview.widget.CardView
            android:layout_width="100dp"
            android:layout_height="150dp"
            app:cardCornerRadius="@dimen/roundedImageRadius"
            android:elevation="10dp"
    >
        <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:id="@+id/result_poster"
                android:clickable="true"
                android:focusable="true"
                tools:src="@drawable/example_poster"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/search_poster_descript"/>
    </androidx.cardview.widget.CardView>-->

</FrameLayout>
