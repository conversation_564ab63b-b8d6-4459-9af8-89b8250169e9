<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/download_child_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/primaryGrayBackground"
    android:orientation="vertical"
    tools:context=".ui.download.DownloadChildFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent">

        <LinearLayout
            android:id="@+id/download_delete_appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="?attr/colorPrimary"
            android:padding="8dp"
            android:visibility="gone">

            <ImageButton
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:src="@drawable/ic_baseline_close_24"
                android:contentDescription="@string/cancel"
                android:padding="8dp"
                android:layout_gravity="center_vertical"
                android:nextFocusLeft="@id/nav_rail_view"
                app:tint="@android:color/white" />

            <Button
                android:id="@+id/btnDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:text="@string/delete"
                android:textColor="@android:color/white"
                android:layout_gravity="center_vertical" />

            <TextView
                android:id="@+id/selectItemsText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:text="@string/downloads_delete_select"
                android:textColor="@android:color/white"
                android:layout_gravity="center_vertical"
                android:visibility="gone" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btnToggleAll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:text="@string/select_all"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:nextFocusDown="@id/download_child_list" />
        </LinearLayout>

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/download_child_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/primaryGrayBackground"
            android:paddingTop="@dimen/navbar_height"
            app:layout_scrollFlags="scroll|enterAlways"
            app:navigationIconTint="?attr/iconColor"
            app:titleTextColor="?attr/textColor"
            tools:title="Overlord" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/download_child_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/primaryBlackBackground"
        android:nextFocusLeft="@id/nav_rail_view"
        android:nextFocusUp="@id/download_child_toolbar"
        android:padding="10dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:listitem="@layout/download_child_episode" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>