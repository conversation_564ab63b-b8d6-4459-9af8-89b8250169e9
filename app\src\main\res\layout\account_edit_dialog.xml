<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_rowWeight="1"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:paddingStart="?android:attr/listPreferredItemPaddingStart"
            android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
            android:text="@string/create_account"
            android:textColor="?attr/textColor"
            android:textSize="20sp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="60dp"
        android:orientation="vertical">

        <EditText
            android:id="@+id/account_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autofillHints="username"
            android:hint="@string/default_account"
            android:inputType="text"
            android:nextFocusLeft="@id/apply_btt"
            android:nextFocusRight="@id/cancel_btt"
            android:nextFocusDown="@id/site_url_input"
            android:requiresFadingEdge="vertical"
            android:textColorHint="?attr/grayTextColor"
            tools:ignore="LabelFor" />

        <CheckBox
            android:id="@+id/lockProfileCheckbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/lock_profile" />

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:cardCornerRadius="@dimen/rounded_image_radius">

            <ImageView
                android:id="@+id/account_image"
                android:src="@drawable/profile_bg_blue"
                android:focusable="true"
                android:contentDescription="@string/preview_background_img_des"
                android:scaleType="centerCrop"
                android:foreground="@drawable/outline_drawable_forced_round"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_gravity="center" />

        </androidx.cardview.widget.CardView>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/apply_btt_holder"
        android:orientation="horizontal"
        android:padding="10dp"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:layout_marginTop="-60dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/delete_btt"
            android:text="@string/delete"
            android:layout_width="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_gravity="center_vertical"
            android:nextFocusRight="@id/apply_btt"
            style="@style/BlackButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/apply_btt"
            android:text="@string/sort_apply"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:layout_toStartOf="@+id/cancel_btt"
            android:nextFocusLeft="@id/delete_btt"
            android:nextFocusRight="@id/cancel_btt"
            style="@style/WhiteButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel_btt"
            android:text="@string/sort_cancel"
            android:layout_width="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_gravity="center_vertical|end"
            android:nextFocusLeft="@id/apply_btt"
            style="@style/BlackButton" />

    </RelativeLayout>
</LinearLayout>