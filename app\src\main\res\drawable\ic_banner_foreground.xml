<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="320dp"
    android:height="180dp"
    android:viewportWidth="320"
    android:viewportHeight="180"
    android:name="vector">
  <group android:scaleX="0.6666667"
      android:scaleY="0.6666667"
      android:translateX="53.333332"
      android:translateY="30">
    <group android:scaleX="0.13764706"
        android:scaleY="0.13764706"
        android:translateX="15.1"
        android:translateY="31.5">
        <group android:name="group_6">
            <path
                android:name="path_2"
                android:pathData="M 698.92 450.33 C 697.92 440.99 690.27 432.74 679.92 430.25 L 653.67 430.75 C 655.17 407.75 636.67 386.63 618.79 384 C 611.804 381.871 604.367 381.698 597.29 383.5 C 597.91 379.77 602.74 347.16 578.79 322 C 558.72 300.92 532.54 299.13 517.67 299.31 L 500.37 298.92 C 494.76 293.4 460.65 262.47 412.62 266.67 C 382.23 269.32 354.96 277.67 334.79 302.67 C 333.11 304.74 327.6 311.81 321.17 320.81 C 318.82 323.81 316.91 327.43 314.79 330.5 C 314.63 330.74 288.79 326.44 272.79 331 C 247.52 338.2 224.79 358.54 217.29 385.5 C 215.44 392.18 209.08 415.09 219.79 435 C 222.101 439.202 223.081 444.009 222.6 448.78 C 219.48 450.41 205.02 450.94 182.29 450 C 164.43 449.27 149.83 463.6 149.79 480 C 149.74 497.17 165.23 510.58 184.29 510 C 205.93 510.23 605.96 510.67 637.04 510.56 C 650.12 510.56 656.79 499.17 656.79 499.17 C 661.24 493.35 661.09 486.09 661.04 481.5 L 661.04 477 L 664.79 477 L 675.96 476.33 C 690.35 474.78 700.21 462.36 698.92 450.33 Z M 407.54 456 L 407.54 349.25 L 483.75 402.63 Z"
                android:fillColor="#ffffff"
                android:strokeWidth="1"/>
        </group>
    </group>

    <group android:scaleX="0.17275657"
        android:scaleY="0.17275657"
        android:translateX="128"
        android:translateY="74.202896">
      <group android:translateY="146.25">
        <path android:pathData="M88,-5.25Q75.984375,2,57.984375,2Q35.0625,2,21.03125,-13.171875Q7,-28.34375,7,-52.421875Q7,-77.6875,22.390625,-94.34375Q37.796875,-111,62.40625,-111Q77.875,-111,88,-106.75L88,-93.75Q76.328125,-100,62.28125,-100Q43,-100,31.5,-86.890625Q20,-73.78125,20,-53.203125Q20,-33.671875,30.765625,-21.328125Q41.53125,-9,59.625,-9Q75.984375,-9,88,-16.25L88,-5.25Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M109,0L109,-115L121,-115L121,0L109,0Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M140,-38.015625Q140,-57.546875,150.71875,-68.765625Q161.4375,-80,179.89062,-80Q197.26562,-80,207.125,-69.15625Q217,-58.328125,217,-39.21875Q217,-20.65625,206.3125,-9.328125Q195.625,2,178.03125,2Q161.01562,2,150.5,-8.90625Q140,-19.8125,140,-38.015625ZM152,-38.5Q152,-24.59375,159.32812,-16.296875Q166.65625,-8,178.89062,-8Q191.34375,-8,198.17188,-16.078125Q205,-24.171875,205,-38.78125Q205,-53.75,198.23438,-61.875Q191.48438,-70,178.89062,-70Q166.59375,-70,159.29688,-61.59375Q152,-53.1875,152,-38.5Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M299,0L287,0L287,-12.15625L286.71875,-12.15625Q279.0625,2,262.79688,2Q235,2,235,-31.28125L235,-78L247,-78L247,-33.25Q247,-8,265.89062,-8Q275.1875,-8,281.09375,-15Q287,-22,287,-32.953125L287,-78L299,-78L299,0Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M390,0L378,0L378,-13.359375L377.71875,-13.359375Q369.125,2,351.01562,2Q336.34375,2,327.67188,-8.578125Q319,-19.171875,319,-37.15625Q319,-56.421875,328.71875,-68.203125Q338.4375,-80,354.375,-80Q370.3125,-80,377.71875,-66.875L378,-66.875L378,-115L390,-115L390,0ZM378,-47.015625Q378,-56.65625,371.54688,-63.328125Q365.09375,-70,355.75,-70Q344.17188,-70,337.57812,-61.3125Q331,-52.625,331,-37.796875Q331,-24.25,337.3125,-16.125Q343.625,-8,354.29688,-8Q364.54688,-8,371.26562,-15.625Q378,-23.25,378,-35.09375L378,-47.015625Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M413,-19.25Q417.5,-14.875,425.96875,-11.9375Q434.4375,-9,441.625,-9Q465,-9,465,-26.265625Q465,-31.09375,462.5625,-34.875Q460.14062,-38.671875,456.01562,-41.4375Q451.89062,-44.21875,439.76562,-50.53125Q422.29688,-59.671875,417.64062,-66.46875Q413,-73.28125,413,-81.859375Q413,-95.125,423.5625,-103.0625Q434.14062,-111,450.125,-111Q466.25,-111,473,-106.75L473,-92.75Q463.65625,-100,448.875,-100Q438.78125,-100,432.39062,-95.53125Q426,-91.0625,426,-82.96875Q426,-77.96875,428.25,-74.28125Q430.51562,-70.59375,434.39062,-67.875Q438.28125,-65.15625,449.53125,-59.46875Q465.35938,-51.53125,471.67188,-43.96875Q478,-36.421875,478,-27.078125Q478,-13.171875,467.9375,-5.578125Q457.875,2,439.76562,2Q434.25,2,425.40625,-0.109375Q416.5625,-2.234375,413,-5.25L413,-19.25Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M533,-1.25Q528.7656,2,521.8125,2Q502,2,502,-20.78125L502,-68L489,-68L489,-78L502,-78L502,-97.1875L514,-101L514,-78L533,-78L533,-68L514,-68L514,-22.859375Q514,-14.828125,516.5781,-11.40625Q519.15625,-8,525.2031,-8Q529.875,-8,533,-11.25L533,-1.25Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M590,-66.75Q586.78125,-70,580.71875,-70Q572.5469,-70,567.2656,-61.953125Q562,-53.90625,562,-40.84375L562,0L550,0L550,-78L562,-78L562,-63.46875L562.34375,-63.46875Q565,-71.328125,570.4375,-75.65625Q575.8906,-80,582.4531,-80Q587.4219,-80,590,-78.75L590,-66.75Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M665,-36L610,-36Q610.3594,-22.453125,617.125,-15.21875Q623.8906,-8,635.6719,-8Q648.8594,-8,660,-17L660,-6Q649.8125,2,633,2Q616.7656,2,607.375,-8.6875Q598,-19.390625,598,-38.78125Q598,-56.625,608.1094,-68.3125Q618.2344,-80,633.4219,-80Q648.34375,-80,656.6719,-70.0625Q665,-60.125,665,-42.390625L665,-36ZM653,-46Q652.9375,-57.375,647.7344,-63.6875Q642.53125,-70,633.1875,-70Q624.6875,-70,618.25,-63.65625Q611.8281,-57.3125,610.1406,-46L653,-46Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M687,-72Q698.3906,-80,713.0781,-80Q740,-80,740,-51.09375L740,0L728,0L728,-12L727.5781,-12Q719.5156,2,703.75,2Q692.25,2,685.625,-4.140625Q679,-10.296875,679,-20.40625Q679,-41.96875,704.5156,-45.625L727.9375,-49Q727.9375,-70,711.9844,-70Q698.03125,-70,687,-60L687,-72ZM708.8281,-36.46875Q698.8281,-35.140625,694.90625,-31.6875Q691,-28.234375,691,-21.046875Q691,-15.25,695.1875,-11.625Q699.3906,-8,706.15625,-8Q715.53125,-8,721.7344,-14.625Q727.9375,-21.265625,727.9375,-31.1875L727.9375,-39L708.8281,-36.46875Z"
            android:fillColor="#FFFFFF"/>
        <path android:pathData="M873,0L861,0L861,-44.90625Q861,-58.546875,857.0156,-64.265625Q853.03125,-70,843.84375,-70Q836.15625,-70,830.5781,-62.5Q825,-55.015625,825,-44.6875L825,0L813,0L813,-46.53125Q813,-70,795.0625,-70Q786.8281,-70,781.40625,-62.859375Q776,-55.71875,776,-44.6875L776,0L764,0L764,-78L776,-78L776,-65.859375L776.34375,-65.859375Q784.5,-80,800.3906,-80Q807.9844,-80,814.0625,-75.53125Q820.15625,-71.0625,822.3281,-63.75Q830.8281,-80,847.7656,-80Q873,-80,873,-48.125L873,0Z"
            android:fillColor="#FFFFFF"/>
      </group>
    </group>
  </group>
</vector>