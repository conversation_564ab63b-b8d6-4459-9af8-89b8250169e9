<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="?attr/primaryBlackBackground"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/edit_account_button"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="end|top"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_baseline_edit_24"
        android:focusable="true"
        android:nextFocusDown="@id/account_recycler_view"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/manage_accounts"
        app:tint="@color/player_on_button_tv_attr" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="36dp"
        android:gravity="center">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_an_account"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp" />

        <com.lagradost.cloudstream3.ui.AutofitRecyclerView
            android:id="@+id/account_recycler_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp" />

    </LinearLayout>
</FrameLayout>