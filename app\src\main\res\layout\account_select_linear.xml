<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
        android:textStyle="bold"
        android:text="@string/switch_account"
        android:textSize="20sp"
        android:textColor="?attr/textColor"
        android:layout_width="match_parent"
        android:layout_rowWeight="1"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/account_recycler_view"
        android:orientation="horizontal"
        android:descendantFocusability="afterDescendants"
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="4"
        tools:listitem="@layout/account_list_item" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/manage_accounts_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="10dp"
        android:text="@string/manage_accounts"
        android:textSize="16sp"
        app:icon="@drawable/ic_baseline_edit_24"
        style="@style/BlackButton" />

</LinearLayout>