<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="1dp"
    android:visibility="gone"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

        <ImageView
            android:id="@+id/nav_header_notification"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:itemIconTint="@color/item_select_color"
            android:focusable="true"
            android:nextFocusRight="@id/home_preview_play_btt"
            android:src="@drawable/notifications_icon_selector"
            android:contentDescription="@string/account"
            app:tint="@color/item_select_color" />

</LinearLayout>