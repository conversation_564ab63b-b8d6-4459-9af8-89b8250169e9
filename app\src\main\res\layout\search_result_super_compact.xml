<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginBottom="3dp">

    <androidx.cardview.widget.CardView
            android:foreground="?android:attr/selectableItemBackgroundBorderless"
            android:id="@+id/background_card"
            android:layout_margin="2dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="10dp"
            android:elevation="0dp"
            app:cardCornerRadius="@dimen/rounded_image_radius"
            app:cardBackgroundColor="?attr/boxItemBackground"
            android:clickable="true"
            android:focusable="true">
        <!-- USING CROP RATIO (182/268), centerCrop for fill -->
        <androidx.cardview.widget.CardView
                app:cardCornerRadius="@dimen/rounded_image_radius"
                android:layout_width="35dp"
                android:elevation="0dp"
                android:layout_height="match_parent">

            <ImageView
                    android:layout_gravity="left"
                    android:id="@+id/imageView"
                    android:scaleType="centerCrop"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:foreground="?android:attr/selectableItemBackgroundBorderless"
                    tools:ignore="RtlHardcoded"
                    android:contentDescription="@string/search_poster_img_des" />
        </androidx.cardview.widget.CardView>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="45dp">

            <TextView
                    tools:text="@string/no_data"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="17sp"
                    android:textColor="?attr/textColor"
                    android:id="@+id/imageText"
                    android:textStyle="normal"
                    android:maxLines="3" />

            <TextView
                    tools:text="@string/no_data"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:textColor="?attr/grayTextColor"
                    android:id="@+id/imageTextExtra"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:maxLines="3" />

            <TextView
                    tools:text="Rated 4.13"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/colorPrimary"
                    android:id="@+id/imageTextProvider"
                    android:maxLines="1" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>