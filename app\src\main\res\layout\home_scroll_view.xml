<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="?attr/primaryGrayBackground"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/home_scroll_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        tools:src="@drawable/example_poster" />

    <View
        android:id="@+id/title_shadow_top"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:layout_gravity="top"
        android:alpha="1"
        android:background="@drawable/background_shadow"
        android:rotation="180"
        android:visibility="visible" />

    <View
        android:id="@+id/title_shadow"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_gravity="bottom"
        android:background="@drawable/background_shadow" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="100dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/home_scroll_preview_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:paddingHorizontal="30dp"
            android:paddingBottom="10dp"
            android:textColor="?attr/white"
            android:textSize="17sp"
            android:textStyle="bold"
            tools:text="The Perfect Run" />
        <!--<TextView
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:id="@+id/home_season_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="5 seasons 50 episodes" />-->
        <TextView
            android:id="@+id/home_scroll_preview_tags"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:textColor="?attr/white"
            android:textSize="14sp"
            tools:text="Hello • World • Tags" />
    </LinearLayout>

</FrameLayout>