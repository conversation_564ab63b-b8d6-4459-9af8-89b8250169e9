<!--<CheckedTextView
        xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
        android:id="@android:id/text1"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/listPreferredItemHeightSmall"
        android:textAppearance="?android:attr/textAppearanceListItemSmall"
        android:gravity="center_vertical"
        android:textColor="?attr/textColor"
        tools:text="Example Text"
        android:background="?attr/bitDarkerGrayBackground"
        android:checkMark="?android:attr/listChoiceIndicatorSingle"
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"/>
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?android:attr/listPreferredItemHeightSmall"
    android:paddingVertical="10dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/drawable_start"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:paddingHorizontal="10dp"
        android:src="@drawable/ic_baseline_check_24_listview"
        tools:ignore="ContentDescription" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/main_text"
            style="@style/CheckLabel"
            android:foreground="@null"
            android:minHeight="@null"
            app:drawableStartCompat="@null"
            tools:text="The Boys - S01E02 Cherry" />

        <TextView
            android:id="@+id/secondary_text"
            style="@style/CheckLabel"
            android:foreground="@null"
            android:minHeight="@null"
            android:padding="0dp"
            android:textSize="12sp"
            app:drawableStartCompat="@null"
            tools:text="English · OpenSubtitles" />
    </LinearLayout>

    <ImageView
        tools:src="@drawable/ic_baseline_hearing_24"
        android:id="@+id/drawable_end"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="0"
        android:paddingHorizontal="10dp"
        tools:ignore="ContentDescription" />
</LinearLayout>

