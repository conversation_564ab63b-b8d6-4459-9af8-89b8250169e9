<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/repository_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/outline_drawable"
    android:nextFocusRight="@id/action_settings"
    android:orientation="horizontal"
    android:clickable="true"
    android:focusable="true"
    android:padding="12dp">

    <ImageView
        android:id="@+id/entry_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="start|center_vertical"
        android:layout_marginEnd="16dp"
        android:scaleType="fitCenter"
        app:srcCompat="@drawable/ic_github_logo" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/main_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/textColor"
            android:textSize="16sp"
            tools:text="Test repository" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/lang_icon"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="5dp"
                android:text="🇷🇼"

                android:textColor="?attr/grayTextColor"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/ext_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="5dp"
                android:text="v1"
                android:textColor="?attr/grayTextColor"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/ext_filesize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="5dp"
                android:text="100MB"
                android:textColor="?attr/grayTextColor"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/ext_votes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="5dp"
                tools:text="Rating: 0"
                android:textColor="?attr/grayTextColor"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/nsfw_marker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/is_adult"
                android:textColor="@color/adultColor"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <TextView
            android:id="@+id/sub_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="?attr/grayTextColor"
            android:textSize="12sp"
            tools:text="https://github.com/..." />

    </LinearLayout>

    <ImageView
        android:id="@+id/action_settings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:padding="12dp"
        android:background="@drawable/outline_drawable"
        android:contentDescription="@string/title_settings"
        android:visibility="gone"
        android:focusable="true"
        android:nextFocusLeft="@id/repository_item_root"
        android:nextFocusRight="@id/action_button"
        app:srcCompat="@drawable/ic_baseline_tune_24"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/action_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|end"
        android:layout_marginStart="10dp"
        android:background="@drawable/outline_drawable"
        android:clickable="true"
        android:contentDescription="@string/download"
        android:focusable="true"
        android:nextFocusLeft="@id/action_settings"
        android:padding="12dp"
        tools:src="@drawable/ic_baseline_add_24" />

</LinearLayout>
