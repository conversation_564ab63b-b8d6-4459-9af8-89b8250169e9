<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. Material3 -->
    <style name="AppTheme" parent="Theme.Material3.Dark.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:navigationBarColor">?attr/primaryBlackBackground</item>
        <item name="android:colorEdgeEffect">?attr/textColor</item>

        <!-- Opt out of edgeToEdge enforcement in >= Android 15 -->
        <!-- However this will be deprecated in future (ETA ~ 1.6 years) -->
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
        <!--<item name="android:navigationBarColor">@color/darkBackground</item>-->
        <item name="android:statusBarColor">?attr/iconGrayBackground</item>

        <item name="android:scrollbarThumbVertical">@null</item>
        <item name="android:scrollbarThumbHorizontal">@null</item>

        <item name="android:windowTranslucentStatus">true</item> <!--true-->
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="alertDialogTheme">@style/Theme.AlertDialog</item>

        <item name="android:textViewStyle">@style/AppTextViewStyle</item>
        <item name="android:buttonStyle">@style/AppButtonStyle</item>
        <item name="android:editTextStyle">@style/AppEditStyle</item>
        <item name="materialButtonStyle">@style/AppMaterialButtonStyle</item>
        <item name="preferenceFragmentCompatStyle">@style/PreferenceTheme</item>
        <item name="bottomSheetDialogTheme">@style/AppBottomSheetDialogTheme</item>
        <item name="searchViewStyle">@style/AppSearchViewStyle</item>
        <item name="android:searchViewStyle">@style/AndroidSearchView</item>

        <item name="tabStyle">@style/Theme.Widget.Tabs</item>
        <item name="android:divider">@color/transparent</item>
        <item name="divider">@color/transparent</item>
        <item name="android:dividerHeight">0dp</item>
        <item name="showDividers">none</item>
        <item name="android:listViewStyle">@style/ListViewStyle</item>

        <item name="castExpandedControllerStyle">
            @style/CustomCastExpandedController
        </item>
        <item name="castMiniControllerStyle">@style/CustomCastMiniController</item>
        <!--<item name="mediaRouteButtonTint">?attr/colorPrimary</item>-->

        <!-- Preference -->
        <item name="android:textColor">?attr/textColor</item>
        <item name="android:textColorSecondary">?attr/grayTextColor</item>

        <item name="android:textColorHint">?attr/grayTextColor</item>
        <item name="android:editTextColor">?attr/textColor</item>
        <item name="android:colorForeground">?attr/textColor</item>
        <item name="android:colorControlHighlight">?attr/textColor</item> <!--iconRipple-->

        <item name="android:windowAllowReturnTransitionOverlap">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">true</item>
        <!--<item name="preferenceTheme">@style/PreferencesTheme</item>-->

        <!-- DEF STYLE -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="textColor">@color/textColor</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <item name="grayTextColor">@color/grayTextColor</item>
        <item name="primaryGrayBackground">@color/primaryGrayBackground</item>
        <item name="primaryBlackBackground">@color/primaryBlackBackground</item>
        <item name="iconGrayBackground">@color/iconGrayBackground</item>
        <item name="boxItemBackground">@color/boxItemBackground</item>
        <item name="iconColor">@color/iconColor</item>
        <item name="white">@color/white</item>
        <item name="black">@color/black</item>
        <item name="focusBackground">?android:attr/selectableItemBackground</item>
        <item name="preferenceTheme">@style/CustomPreferenceThemeOverlay</item>

        <item name="download_icon_color">?attr/white</item>
        <item name="download_fill_color">?attr/white</item>
        <item name="download_outline_color">?attr/white</item>
        <item name="download_icon_scale">1.0</item>

        <item name="cpv_focusBorderColor">?attr/white</item>
    </style>

    <style name="AppThemeTvOverlay">
        <item name="focusBackground">@drawable/outline_drawable_less</item>
    </style>

    <style name="ListViewStyle" parent="Widget.AppCompat.ListView">
        <item name="android:divider">@null</item>
        <item name="android:listSelector">@drawable/outline_drawable_forced</item>
        <item name="android:drawSelectorOnTop">false</item>
    </style>

    <style name="AmoledModeLight" parent="AmoledMode">
        <item name="primaryGrayBackground">@color/amoledModeLight</item>
    </style>

    <style name="ChipFilled" parent="@style/Widget.Material3.Chip.Filter">
        <item name="chipBackgroundColor">@color/chip_color</item>
        <item name="chipStrokeColor">@color/white_transparent_toggle</item>
        <!--        <item name="chipStrokeColor">@color/transparent</item>-->
        <item name="chipStrokeWidth">2dp</item>
        <item name="textColor">@color/chip_color_text</item>
        <item name="android:textColor">@color/chip_color_text</item>
        <item name="checkedIconTint">@color/chip_color_text</item>
        <item name="fontFamily">@font/google_sans</item>
        <item name="chipIconTint">@color/chip_color_text</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:tag">@string/tv_no_focus_tag</item>
        <item name="chipMinTouchTargetSize">0dp</item>
    </style>

    <style name="RoundProgressbar">
        <item name="indicatorTrackGapSize">0dp</item>
        <item name="trackStopIndicatorSize">0dp</item>
        <item name="trackColor">@color/color_primary_transparent</item>
    </style>

    <style name="ChipFilledSemiTransparent" parent="@style/ChipFilled">
        <item name="chipBackgroundColor">@color/transparent</item>
        <item name="chipSurfaceColor">@color/white_attr_20</item>
        <item name="backgroundColor">@color/transparent</item>
    </style>

    <style name="ChipParent">
        <item name="chipSpacingVertical">-5dp</item>
        <item name="chipSpacingHorizontal">5dp</item>
        <item name="textColor">?attr/textColor</item>
    </style>

    <style name="AmoledMode">
        <item name="primaryGrayBackground">@color/black</item>
        <item name="primaryBlackBackground">@color/black</item>
        <item name="iconGrayBackground">@color/primaryBlackBackground</item>
        <item name="boxItemBackground">@color/black</item>
        <item name="textColor">@color/textColor</item>
        <item name="grayTextColor">@color/grayTextColor</item>
        <item name="white">@color/white</item>
        <item name="black">@color/black</item>
        <item name="colorOnPrimary">@color/whiteText</item>
    </style>

    <style name="DubButton" parent="@style/SearchBox">
        <item name="android:background">@drawable/dub_bg_color</item>
        <item name="android:text">@string/app_dubbed_text</item>
        <item name="android:textColor">@color/dubColorText</item>
    </style>

    <style name="SubButton" parent="@style/SearchBox">
        <item name="android:background">@drawable/sub_bg_color</item>
        <item name="android:text">@string/app_subbed_text</item>
        <item name="android:textColor">@color/subColorText</item>
    </style>

    <!--<style name="RatingButton" parent="@style/SearchBox">
        <item name="android:background">@drawable/rating_bg_color</item>
        <item name="android:textColor">@color/ratingColor</item>
    </style>-->

    <style name="RatingButton" parent="@style/SearchBox">
        <item name="android:minWidth">40dp</item>
        <item name="android:background">@drawable/rating_bg_color</item>
        <item name="drawableTint">?attr/colorOnPrimary</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <!--<item name="drawableStartCompat">@drawable/ic_baseline_star_24</item>-->
    </style>

    <style name="TypeButton" parent="@style/SearchBox">
        <item name="android:background">@drawable/type_bg_color</item>
        <item name="android:text">@string/quality_hd</item>
        <item name="android:textColor">@color/typeColorText</item>
    </style>

    <style name="LightMode">
        <item name="primaryGrayBackground">@color/lightPrimaryGrayBackground</item>
        <item name="primaryBlackBackground">@color/lightBitDarkerGrayBackground</item>
        <item name="iconGrayBackground">@color/lightGrayBackground</item>
        <item name="boxItemBackground">@color/lightItemBackground</item>
        <item name="textColor">@color/lightTextColor</item>
        <item name="grayTextColor">@color/lightGrayTextColor</item>
        <item name="white">@color/black</item>
        <item name="black">@color/white</item>

        <item name="colorOnPrimary">@color/blackText</item>
    </style>

    <style name="MonetMode">
        <item name="primaryGrayBackground">@color/material_dynamic_neutral20</item>
        <item name="primaryBlackBackground">@color/material_dynamic_neutral10</item>
        <item name="iconGrayBackground">@color/material_dynamic_neutral20</item>
        <item name="boxItemBackground">@color/material_dynamic_neutral20</item>
        <item name="textColor">@color/material_dynamic_neutral90</item>
        <item name="grayTextColor">@color/material_dynamic_neutral60</item>
        <item name="white">@color/material_dynamic_neutral90</item>
        <item name="black">@color/material_dynamic_neutral10</item>
        <item name="colorOnPrimary">@color/material_on_primary_emphasis_medium</item>
    </style>

    <style name="OverlayPrimaryColorNormal">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!-- Needed for leanback fuckery -->
        <item name="android:colorAccent">@color/colorAccent</item>
    </style>

    <style name="OverlayPrimaryColorMonet">
        <item name="colorPrimary">@color/material_dynamic_primary80</item>
        <item name="android:colorPrimary">@color/material_dynamic_primary80</item>
        <item name="colorPrimaryDark">@color/material_dynamic_primary30</item>
        <item name="colorAccent">@color/material_dynamic_primary80</item>
        <item name="colorOnPrimary">@color/material_dynamic_primary20</item>
        <!-- Needed for leanback fuckery -->
        <item name="android:colorAccent">@color/material_dynamic_primary80</item>
    </style>

    <style name="OverlayPrimaryColorMonetTwo">
        <item name="colorPrimary">@color/material_dynamic_secondary80</item>
        <item name="android:colorPrimary">@color/material_dynamic_secondary80</item>
        <item name="colorPrimaryDark">@color/material_dynamic_secondary30</item>
        <item name="colorAccent">@color/material_dynamic_secondary80</item>
        <item name="colorOnPrimary">@color/material_dynamic_secondary20</item>
        <!-- Needed for leanback fuckery -->
        <item name="android:colorAccent">@color/material_dynamic_secondary80</item>
    </style>

    <style name="OverlayPrimaryColorBlue">
        <item name="colorPrimary">@color/colorPrimaryBlue</item>
        <item name="android:colorPrimary">@color/colorPrimaryBlue</item>
        <item name="colorPrimaryDark">#4855A2</item>
        <item name="colorAccent">#5A6BCB</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!-- Needed for leanback fuckery -->
        <item name="android:colorAccent">@color/colorPrimaryBlue</item>
    </style>

    <style name="OverlayPrimaryColorPurple">
        <item name="colorPrimary">@color/colorPrimaryPurple</item>
        <item name="android:colorPrimary">@color/colorPrimaryPurple</item>
        <item name="colorPrimaryDark">#4704A3</item>
        <item name="colorAccent">#7125DB</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <item name="android:colorAccent">@color/colorPrimaryPurple</item>
    </style>

    <style name="OverlayPrimaryColorGreen">
        <item name="colorPrimary">@color/colorPrimaryGreen</item>
        <item name="android:colorPrimary">@color/colorPrimaryGreen</item>
        <item name="colorPrimaryDark">#007363</item>
        <item name="colorAccent">#39C1AE</item>
        <item name="colorOnPrimary">@color/blackText</item>
        <item name="android:colorAccent">@color/colorPrimaryGreen</item>
    </style>

    <style name="OverlayPrimaryColorGreenApple">
        <item name="colorPrimary">@color/colorPrimaryGreenApple</item>
        <item name="android:colorPrimary">@color/colorPrimaryGreenApple</item>
        <item name="colorPrimaryDark">#319B5A</item>
        <item name="colorAccent">#51C57E</item>
        <item name="colorOnPrimary">@color/blackText</item>
        <item name="android:colorAccent">@color/colorPrimaryGreenApple</item>
    </style>

    <style name="OverlayPrimaryColorRed">
        <item name="colorPrimary">@color/colorPrimaryRed</item>
        <item name="android:colorPrimary">@color/colorPrimaryRed</item>
        <item name="colorPrimaryDark">#B62B2B</item>
        <item name="colorAccent">@color/colorPrimaryRed</item> <!--#F53B3B-->
        <item name="colorOnPrimary">@color/whiteText</item> <!--#EC3838-->
        <!-- Needed for leanback fuckery -->
        <item name="android:colorAccent">@color/colorPrimaryRed</item>
    </style>

    <style name="OverlayPrimaryColorBanana">
        <item name="colorPrimary">@color/colorPrimaryBanana</item>
        <item name="android:colorPrimary">@color/colorPrimaryBanana</item>
        <item name="colorPrimaryDark">#9B7D31</item>
        <item name="colorAccent">#C5B251</item>
        <item name="colorOnPrimary">@color/blackText</item>
        <item name="android:colorAccent">@color/colorPrimaryBanana</item>
    </style>

    <style name="OverlayPrimaryColorParty">
        <item name="colorPrimary">@color/colorPrimaryParty</item>
        <item name="android:colorPrimary">@color/colorPrimaryParty</item>
        <item name="colorPrimaryDark">#C1495B</item>
        <item name="colorAccent">#FD798C</item>
        <item name="colorOnPrimary">@color/blackText</item>
        <item name="android:colorAccent">@color/colorPrimaryParty</item>
    </style>

    <style name="OverlayPrimaryColorPink">
        <item name="colorPrimary">@color/colorPrimaryPink</item>
        <item name="android:colorPrimary">@color/colorPrimaryPink</item>
        <item name="colorPrimaryDark">#DD1280</item>
        <item name="colorAccent">#FF4DAE</item>
        <item name="colorOnPrimary">@color/blackText</item>
        <item name="android:colorAccent">@color/colorPrimaryPink</item>
    </style>

    <style name="OverlayPrimaryColorCarnationPink">
        <item name="colorPrimary">@color/colorPrimaryCarnationPink</item>
        <item name="android:colorPrimary">@color/colorPrimaryCarnationPink</item>
        <item name="colorPrimaryDark">#83366f</item>
        <item name="colorAccent">#BD5DA5</item>
        <item name="colorOnPrimary">@color/blackText</item>
        <!--   Needed for leanback fuckery   -->
        <item name="android:colorAccent">@color/colorPrimaryCarnationPink</item>
    </style>

    <style name="OverlayPrimaryColorMaroon">
        <item name="colorPrimary">@color/colorPrimaryMaroon</item>
        <item name="android:colorPrimary">@color/colorPrimaryMaroon</item>
        <item name="colorPrimaryDark">#370C0C</item>
        <item name="colorAccent">#451010</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--   Needed for leanback fuckery   -->
        <item name="android:colorAccent">@color/colorPrimaryMaroon</item>
    </style>

    <style name="OverlayPrimaryColorDarkGreen">
        <item name="colorPrimary">@color/colorPrimaryDarkGreen</item>
        <item name="android:colorPrimary">@color/colorPrimaryDarkGreen</item>
        <item name="colorPrimaryDark">#003d00</item>
        <item name="colorAccent">#004500</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--   Needed for leanback fuckery   -->
        <item name="android:colorAccent">@color/colorPrimaryDarkGreen</item>
    </style>

    <style name="OverlayPrimaryColorNavyBlue">
        <item name="colorPrimary">@color/colorPrimaryNavyBlue</item>
        <item name="android:colorPrimary">@color/colorPrimaryNavyBlue</item>
        <item name="colorPrimaryDark">#000073</item>
        <item name="colorAccent">#000080</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--   Needed for leanback fuckery   -->
        <item name="android:colorAccent">@color/colorPrimaryNavyBlue</item>
    </style>

    <style name="OverlayPrimaryColorGrey">
        <item name="colorPrimary">@color/colorPrimaryGrey</item>
        <item name="android:colorPrimary">@color/colorPrimaryGrey</item>
        <item name="colorPrimaryDark">#484848</item>
        <item name="colorAccent">#515151</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--   Needed for leanback fuckery   -->
        <item name="android:colorAccent">@color/colorPrimaryGrey</item>
    </style>

    <style name="OverlayPrimaryColorWhite">
        <item name="colorPrimary">@color/colorPrimaryWhite</item>
        <item name="android:colorPrimary">@color/colorPrimaryWhite</item>
        <item name="colorPrimaryDark">#CCCCCC</item>
        <item name="colorAccent">#FFFFFF</item>
        <item name="colorOnPrimary">@color/blackText</item>
        <!--   Needed for leanback fuckery   -->
        <item name="android:colorAccent">@color/colorPrimaryWhite</item>
    </style>

    <style name="OverlayPrimaryColorBrown">
        <item name="colorPrimary">@color/colorPrimaryBrown</item>
        <item name="android:colorPrimary">@color/colorPrimaryBrown</item>
        <item name="colorPrimaryDark">#582700</item>
        <item name="colorAccent">#622C00</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--   Needed for leanback fuckery   -->
        <item name="android:colorAccent">@color/colorPrimaryBrown</item>
    </style>

    <style name="OverlayPrimaryColorOrange">
        <item name="colorPrimary">@color/colorPrimaryOrange</item>
        <item name="android:colorPrimary">@color/colorPrimaryOrange</item>
        <item name="colorPrimaryDark">#A66B00</item>
        <item name="colorAccent">#CE8500</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--    Needed for leanback fuckery    -->
        <item name="android:colorAccent">@color/colorPrimaryOrange</item>
    </style>

    <style name="OverlayPrimaryColorDandelionYellow">
        <item name="colorPrimary">@color/colorPrimaryDandelionYellow</item>
        <item name="android:colorPrimary">@color/colorPrimaryDandelionYellow</item>
        <item name="colorPrimaryDark">#C49600</item>
        <item name="colorAccent">#F5BB00</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--    Needed for leanback fuckery    -->
        <item name="android:colorAccent">@color/colorPrimaryDandelionYellow</item>
    </style>

    <style name="OverlayPrimaryColorCoolBlue">
        <item name="colorPrimary">@color/colorPrimaryCoolBlue</item>
        <item name="android:colorPrimary">@color/colorPrimaryCoolBlue</item>
        <item name="colorPrimaryDark">#306981</item>
        <item name="colorAccent">#51b0d7</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--    Needed for leanback fuckery    -->
        <item name="android:colorAccent">@color/colorPrimaryCoolBlue</item>
    </style>

    <style name="OverlayPrimaryColorLavender">
        <item name="colorPrimary">@color/colorPrimaryLavender</item>
        <item name="android:colorPrimary">@color/colorPrimaryLavender</item>
        <item name="colorPrimaryDark">#6B51AB</item>
        <item name="colorAccent">#7961B4</item>
        <item name="colorOnPrimary">@color/whiteText</item>
        <!--    Needed for leanback fuckery    -->
        <item name="android:colorAccent">@color/colorPrimaryLavender</item>
    </style>

    <style name="customRatingBar" parent="@style/Widget.AppCompat.RatingBar">

        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
    </style>

    <style name="LoadedStyle">
        <item name="android:navigationBarColor">?attr/primaryGrayBackground</item>
        <item name="android:windowBackground">?attr/primaryBlackBackground</item>
    </style>

    <style name="AndroidSearchView" parent="AppSearchViewStyle">
        <item name="android:paddingStart">-10dp</item>
    </style>

    <style name="AppSearchViewStyle" parent="Theme.MaterialComponents.NoActionBar">
        <item name="android:searchIcon">@drawable/search_icon</item>
        <item name="android:queryHint">@string/search_hint</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:fontFamily">@font/google_sans</item>
    </style>

    <style name="AppBottomSheetDialogTheme">
        <item name="android:navigationBarColor">?attr/boxItemBackground</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/AppModalStyle</item>
    </style>

    <style name="AppModalStyle">
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">true</item>
        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="backgroundTint">?attr/primaryBlackBackground</item>
        <item name="android:background">@drawable/rounded_dialog</item>
        <item name="behavior_peekHeight">512dp</item>
    </style>

    <style name="PreferenceTheme" parent="@style/AppTheme" />

    <style name="Theme.AlertDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
        <item name="android:windowBackground">@drawable/dialog__window_background</item>
        <item name="textAllCaps">false</item>

        <item name="buttonBarPositiveButtonStyle">@style/WhiteButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlackButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/BlackButton.Dialog</item>
    </style>

    <style name="TabNoCaps" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>

    <style name="AppTextViewStyle" parent="android:Widget.TextView">
        <item name="android:fontFamily">@font/google_sans</item>
    </style>

    <style name="AppButtonStyle" parent="android:Widget.Holo.Button">
        <item name="android:fontFamily">@font/google_sans</item>
    </style>

    <style name="AppEditStyle" parent="android:Widget.EditText">
        <item name="android:fontFamily">@font/google_sans</item>
    </style>

    <style name="ResultInfoText">
        <item name="android:layout_gravity">center_vertical</item>
        <item name="textColor">?attr/white</item>

        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">24dp</item>
        <item name="android:minWidth">0dp</item>
        <!--   <item name="android:paddingStart">5dp</item>
           <item name="android:paddingEnd">5dp</item>-->
    </style>

    <style name="AppMaterialButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:fontFamily">@font/google_sans</item>
    </style>

    <style name="Theme.Widget.Tabs" parent="Widget.MaterialComponents.TabLayout.Colored">
        <!--<item name="tabGravity">center</item>
        <item name="backgroundTint">@color/transparent</item>
        <item name="tabIndicator">@drawable/tab_selector</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabTextColor">?attr/colorPrimary</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabIndicatorHeight">3dp</item>
        <item name="tabInlineLabel">true</item>
        <item name="tabMinWidth">75dp</item>
        <item name="tabMode">scrollable</item>-->
    </style>

    <style name="DarkFragment" parent="AppTheme">
        <item name="android:navigationBarColor">?attr/colorPrimary</item>
    </style>

    <style name="AlertDialogCustom" parent="Theme.AppCompat.Dialog.Alert">
        <item name="android:windowFullscreen">true</item>
        <item name="android:textColor">?attr/textColor</item>
        <item name="android:textColorPrimary">?attr/textColor</item>
        <!--<item name="android:background">@color/darkBackground</item>-->
        <item name="android:textAllCaps">false</item>
        <!--<item name="android:colorBackground">@color/darkBackground</item>-->
        <item name="textColorAlertDialogListItem">?attr/textColor</item>
        <item name="colorControlNormal">?attr/textColor</item>
        <!-- colorPrimarySecond used because colorPrimary fails for no reason -->
        <item name="colorControlActivated">?attr/colorPrimary</item>

        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
        <item name="android:windowBackground">@drawable/dialog__window_background</item>
    </style>

    <style name="AlertDialogCustomTransparent" parent="Theme.AppCompat.Dialog.Alert">
        <item name="android:windowBackground">@color/transparent</item>
    </style>

    <style name="DialogFullscreen">
        <item name="android:windowEnterAnimation">@anim/enter_anim</item>
        <item name="android:windowExitAnimation">@anim/exit_anim</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="DialogHalfFullscreen" parent="AlertDialogCustom">
        <item name="android:windowEnterAnimation">@anim/enter_anim</item>
        <item name="android:windowExitAnimation">@anim/exit_anim</item>

        <item name="android:windowMinWidthMajor">45%</item> <!-- 65% -->
        <item name="android:windowMinWidthMinor">95%</item> <!-- 95% -->
    </style>

    <style name="AlertDialogCustomBlack" parent="Theme.AppCompat.Dialog.Alert">
        <item name="android:windowBackground">?attr/primaryBlackBackground</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>

        <!-- No backgrounds, titles or window float -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:navigationBarColor">?attr/primaryBlackBackground</item>
    </style>

    <style name="PopupMenu" parent="@android:style/Widget.PopupMenu">
        <item name="android:backgroundTint">?attr/primaryBlackBackground</item>
    </style>

    <style name="CustomPreferenceThemeOverlay" parent="@style/PreferenceThemeOverlay">
        <item name="android:layout">@layout/settings_title_top</item>
        <item name="preferenceStyle">@style/CustomPreferenceStyle</item>
        <item name="switchPreferenceStyle">@style/CustomSwitchPreferenceStyle</item>
        <item name="seekBarPreferenceStyle">@style/CustomSeekBarPreferenceStyle</item>
    </style>

    <!--
    <style name="CustomPreferenceCategoryStyle" parent="@style/Preference.Category.Material">
        <item name="android:layout">@layout/custom_preference_category_material</item>
    </style>-->

    <style name="CustomSwitchPreferenceStyle" parent="@style/Preference.SwitchPreference.Material">
        <item name="android:layout">@layout/custom_preference_material</item>
    </style>

    <style name="CustomSeekBarPreferenceStyle" parent="@style/Preference.SeekBarPreference.Material">
        <item name="android:layout">@layout/custom_preference_widget_seekbar</item>
    </style>

    <style name="CustomPreferenceStyle" parent="@style/Preference.Material">
        <item name="android:layout">@layout/custom_preference_material</item>
    </style>

    <style name="SettingsItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">50sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="android:selectAllOnFocus">true</item>
        <item name="android:background">?attr/focusBackground</item>
        <item name="android:textColor">?attr/textColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceListItemSmall</item>
        <item name="drawableEndCompat">@drawable/ic_baseline_keyboard_arrow_right_24</item>
    </style>

    <style name="WatchHeaderText">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">?attr/textColor</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">18sp</item>
        <item name="android:layout_marginEnd">50dp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="SearchBox">
        <item name="android:textStyle">bold</item>
        <item name="android:minWidth">50dp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:paddingLeft">7dp</item>
        <item name="android:paddingRight">7dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:textSize">12sp</item>
        <item name="textColor">@color/textColor</item>
        <item name="android:textColor">@color/textColor</item>
        <item name="android:layout_margin">2dp</item>
    </style>

    <style name="NiceButton">
        <!--removes shadow-->
        <item name="android:stateListAnimator">@null</item>

        <item name="android:padding">5dp</item>
        <item name="android:layout_marginStart">5dp</item>
        <item name="android:layout_marginEnd">5dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="iconGravity">textStart</item>
        <item name="iconSize">20dp</item>
        <item name="cornerRadius">@dimen/rounded_button_radius</item>
        <item name="android:textSize">15sp</item>

        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:foreground">@drawable/outline_drawable_less</item>
        <item name="android:tag">@string/tv_no_focus_tag</item>
    </style>

    <style name="WhiteButton" parent="NiceButton">
        <item name="strokeColor">?attr/iconGrayBackground</item>
        <item name="backgroundTint">?attr/textColor</item>
        <item name="iconTint">?attr/iconGrayBackground</item>
        <item name="android:textColor">?attr/iconGrayBackground</item>
        <item name="rippleColor">?attr/primaryBlackBackground</item>
    </style>
    <!-- This is so shit, because android has no native way to either changing the layout (android:layout) or changing the layout_height for no reason.
    So the only way to shrink the buttons is by using insetTop+insetBottom along with the drawable.
    https://android.googlesource.com/platform/frameworks/base/+/master/core/res/res/layout/alert_dialog.xml <- old xml for reference, but new use m3_alert_dialog
    -->
    <style name="BlackButton.Dialog" parent="BlackButton">
        <item name="android:foreground">@drawable/outline_drawable_less_inset</item>
        <item name="android:insetTop">@dimen/dialog_buttons_inset</item>
        <item name="android:insetBottom">@dimen/dialog_buttons_inset</item>
    </style>

    <style name="WhiteButton.Dialog" parent="WhiteButton">
        <item name="android:foreground">@drawable/outline_drawable_less_inset</item>
        <item name="android:insetTop">@dimen/dialog_buttons_inset</item>
        <item name="android:insetBottom">@dimen/dialog_buttons_inset</item>
    </style>

    <style name="BlackLabel" parent="@style/Widget.MaterialComponents.Tooltip">
        <item name="backgroundTint">?attr/iconGrayBackground</item>
        <item name="android:textColor">?attr/textColor</item>
    </style>

    <style name="CheckLabel" parent="@style/NoCheckLabel">

        <!--        <item name="drawableTint">@color/check_selection_color</item>-->
        <!--        Set color in the drawable instead of tint to allow multiple drawables-->
        <item name="android:checkMark">?android:attr/listChoiceIndicatorSingle</item>
        <item name="drawableStartCompat">@drawable/ic_baseline_check_24_listview</item>
    </style>


    <style name="NoCheckLabel" parent="@style/AppTextViewStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">?android:attr/listPreferredItemHeightSmall</item>
        <item name="android:textColor">@color/text_selection_color</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:foreground">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:drawablePadding">20dp</item>
    </style>


    <style name="BlackButton" parent="NiceButton">
        <item name="strokeColor">?attr/textColor</item>
        <item name="backgroundTint">?attr/iconGrayBackground</item>
        <item name="iconTint">?attr/textColor</item>
        <item name="android:textColor">?attr/textColor</item>
        <item name="rippleColor">?attr/textColor</item>
    </style>

    <style name="SmallBlackButton" parent="BlackButton">
        <item name="android:layout_height">24dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_marginStart">0dp</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="iconPadding">0dp</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:minWidth">0dp</item>
    </style>

    <style name="SmallWhiteButton" parent="WhiteButton">
        <item name="android:layout_height">24dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_marginStart">0dp</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="iconPadding">0dp</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:minWidth">0dp</item>
    </style>

    <style name="RoundedSelectableButtonIcon" parent="RoundedSelectableButton">
        <item name="minWidth">0dp</item>
        <item name="iconTint">?attr/textColor</item>
        <item name="iconGravity">textStart</item>
        <item name="iconPadding">0dp</item>
        <item name="android:layout_width">35dp</item>
        <!--<item name="android:layout_height">35dp</item>-->
    </style>

    <style name="ExtendedFloatingActionButton">
        <item name="elevation">0dp</item>
        <item name="android:textStyle">bold</item>
        <item name="backgroundTint">?attr/primaryGrayBackground</item>
        <item name="tint">?attr/colorPrimary</item>
        <item name="textColor">?attr/colorPrimary</item>
        <item name="iconTint">?attr/textColor</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:layout_gravity">end|bottom</item>
        <item name="textAllCaps">false</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="RoundedSelectableButton">
        <item name="backgroundTint">@color/toggle_button</item>
        <item name="rippleColor">@color/textColor</item>
        <item name="android:textColor">@color/toggle_button_text</item>
        <item name="cornerRadius">100dp</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">@color/toggle_button_outline</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">45dp</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <!--<item name="android:layout_marginStart">10dp</item>-->
        <item name="android:layout_marginEnd">5dp</item>
        <item name="textAllCaps">false</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="MultiSelectButton" parent="BlackButton">
        <item name="android:layout_height">40dp</item>
        <item name="android:layout_width">wrap_content</item>

        <item name="strokeColor">?attr/textColor</item>
        <item name="backgroundTint">?attr/iconGrayBackground</item>
        <item name="iconTint">?attr/textColor</item>
        <item name="android:textColor">?attr/textColor</item>
        <item name="rippleColor">?attr/textColor</item>
    </style>

    <style name="SelectableButton" parent="NiceButton">
        <item name="android:layout_height">40dp</item>
        <item name="android:layout_width">wrap_content</item>

        <item name="strokeColor">@color/selectable_black</item>
        <item name="backgroundTint">@color/selectable_white</item>
        <item name="iconTint">@color/selectable_black</item>
        <item name="android:textColor">@color/selectable_black</item>
        <item name="rippleColor">@color/selectable_black</item>
    </style>

    <style name="VideoButton">
        <item name="android:stateListAnimator">@null</item>
        <item name="strokeColor">@color/transparent</item>
        <item name="backgroundTint">@color/transparent</item>
        <item name="rippleColor">@color/video_button_ripple</item>
        <item name="android:shadowColor">@color/transparent</item>

        <item name="cornerRadius">3dp</item>
        <item name="iconTint">@color/white</item>
        <item name="textColor">@color/white</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">45dp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="textAllCaps">false</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">10sp</item>
        <item name="android:layout_marginStart">10dp</item>
        <item name="android:layout_marginEnd">10dp</item>
    </style>

    <style name="SyncButton" parent="NiceButton">
        <item name="rippleColor">?attr/white</item>
        <item name="iconTint">?attr/textColor</item>
        <item name="android:layout_columnWeight">1</item>
        <item name="android:layout_rowWeight">1</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">?attr/textColor</item>
        <item name="backgroundTint">@color/toggle_button</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">@color/toggle_button_outline</item>
    </style>

    <style name="Tag" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/tag_stroke_color</item>
    </style>

    <style name="ResultButtonTV" parent="@style/RegularButtonTV">
        <item name="android:tag">@string/tv_no_focus_tag</item>
        <item name="iconGravity">start</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_width">250dp</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:layout_marginStart">0dp</item>
        <item name="android:layout_marginEnd">0dp</item>
    </style>

    <style name="SelectableButtonTV" parent="RegularButtonTV">
        <item name="icon">@drawable/ic_baseline_check_24_listview</item>
        <item name="iconTint">@color/button_selector_color</item>
    </style>

    <style name="RegularButtonTV">
        <item name="android:tag">@string/tv_no_focus_tag</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="strokeColor">@color/transparent</item>
        <item name="backgroundTint">@null</item>
        <item name="android:background">@drawable/player_button_tv_attr</item>
        <item name="rippleColor">@color/white</item>
        <item name="android:shadowColor">@color/transparent</item>

        <item name="iconTint">@color/player_on_button_tv_attr</item>
        <item name="textColor">@color/player_on_button_tv_attr</item>
        <item name="android:textColor">@color/player_on_button_tv_attr</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">40dp</item>
        <item name="iconSize">16dp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:baselineAligned">false</item>
        <item name="textAllCaps">false</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:layout_marginStart">4dp</item>
        <item name="android:layout_marginEnd">4dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:insetTop">0dp</item>
    </style>

    <style name="ResultSmallButtonTV">
        <item name="android:tag">@string/tv_no_focus_tag</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="strokeColor">@color/transparent</item>
        <item name="backgroundTint">@null</item>
        <item name="android:background">@drawable/player_button_tv_attr</item>
        <item name="rippleColor">@color/white</item>
        <item name="android:shadowColor">@color/transparent</item>
        <item name="iconTint">@color/player_on_button_tv_attr</item>
        <item name="iconGravity">textStart</item>
        <item name="android:layout_width">60dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginStart">4dp</item>
        <item name="android:layout_marginEnd">4dp</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>

    <style name="ResultMarqueeButtonText">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">top|center_horizontal</item>
        <item name="android:singleLine">true</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:ellipsize">marquee</item>
    </style>

    <style name="VideoButtonTV">
        <item name="android:tag">@string/tv_no_focus_tag</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="strokeColor">@color/transparent</item>
        <item name="backgroundTint">@null</item>
        <item name="android:background">@drawable/player_button_tv</item>
        <item name="rippleColor">@color/white</item>
        <item name="android:shadowColor">@color/transparent</item>

        <item name="iconTint">@color/player_on_button_tv</item>
        <item name="textColor">@color/player_on_button_tv</item>
        <item name="android:textColor">@color/player_on_button_tv</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">25dp</item>
        <item name="iconSize">16dp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:baselineAligned">false</item>
        <item name="textAllCaps">false</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">10sp</item>
        <item name="android:layout_marginStart">4dp</item>
        <item name="android:layout_marginEnd">4dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:insetTop">0dp</item>
    </style>

    <!--@color/white ?attr/colorPrimary-->
    <!--CHECK ?attr/darkBackground ?attr/colorPrimary-->
    <!-- CHROMECAST -->
    <style name="CustomCastExpandedController" parent="CastExpandedController">
        <item name="castControlButtons">
            @array/cast_expanded_controller_control_buttons
        </item>
        <!-- <item name="castButtonColor">@null</item>

         <item name="castSeekBarSecondaryProgressColor">@color/darkBar
         </item>
         -->
        <item name="castSeekBarProgressAndThumbColor">?attr/colorPrimary</item>
        <item name="castBackground">?attr/colorPrimary</item>
        <item name="castProgressBarColor">?attr/colorPrimary</item>
        <item name="castPlayButtonDrawable">@drawable/ic_baseline_play_arrow_24</item>
        <item name="castPauseButtonDrawable">@drawable/netflix_pause</item>
        <item name="castStopButtonDrawable">@drawable/cast_ic_expanded_controller_stop</item>
        <item name="castSkipPreviousButtonDrawable">
            @drawable/cast_ic_expanded_controller_skip_previous
        </item>
        <item name="castSkipNextButtonDrawable">@drawable/cast_ic_expanded_controller_skip_next
        </item>
        <item name="castRewind30ButtonDrawable">@drawable/go_back_30</item>
        <item name="castForward30ButtonDrawable">@drawable/go_forward_30</item>
    </style>

    <style name="CustomCastMiniController" parent="CastMiniController">
        <item name="castMiniControllerLoadingIndicatorColor">?attr/colorPrimary</item>
        <item name="castShowImageThumbnail">true</item>

        <item name="castTitleTextAppearance">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="castSubtitleTextAppearance">@style/TextAppearance.AppCompat.Caption</item>
        <item name="castBackground">@color/transparent
        </item> <!--CHECK bitDarkerGrayBackground darkBackground-->
        <item name="castProgressBarColor">?attr/colorPrimary</item>
        <item name="castStopButtonDrawable">@drawable/cast_ic_mini_controller_stop</item>'
        <item name="castLargeStopButtonDrawable">@drawable/cast_ic_mini_controller_stop_large</item>
        <item name="castSkipPreviousButtonDrawable">@drawable/cast_ic_mini_controller_skip_prev
        </item>
        <item name="castSkipNextButtonDrawable">@drawable/cast_ic_mini_controller_skip_next</item>
        <item name="castRewind30ButtonDrawable">@drawable/go_back_30</item>
        <item name="castForward30ButtonDrawable">@drawable/go_forward_30</item>
        <item name="castMuteToggleButtonDrawable">@drawable/cast_ic_mini_controller_mute</item>
        <item name="castClosedCaptionsButtonDrawable">
            @drawable/cast_ic_mini_controller_closed_caption
        </item>
    </style>
</resources>
