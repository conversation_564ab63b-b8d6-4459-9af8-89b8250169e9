<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setup_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="@string/extensions"
        android:textSize="18sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/setup_extensions_subtext" />

    <LinearLayout
        android:id="@+id/blank_repo_screen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="20dp"
        android:gravity="center"
        android:orientation="vertical"
        tools:visibility="gone">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_baseline_extension_24" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="@string/blank_repo_message"
            android:textSize="16sp" />

<!--        <com.google.android.material.button.MaterialButton-->
<!--            android:id="@+id/list_repositories"-->
<!--            style="@style/WhiteButton"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:text="@string/view_public_repositories_button" />-->
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/repo_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/repository_item"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/apply_btt_holder"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:layout_marginTop="-60dp"
        android:gravity="bottom|end"
        android:orientation="horizontal">

<!--        <com.google.android.material.button.MaterialButton-->
<!--            android:id="@+id/view_public_repositories_button"-->
<!--            style="@style/WhiteButton"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_gravity="center_vertical|end"-->
<!--            android:text="@string/add_repository" />-->

        <com.google.android.material.button.MaterialButton
            android:id="@+id/next_btt"
            style="@style/WhiteButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/next" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/prev_btt"
            style="@style/BlackButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/previous" />
    </LinearLayout>


</LinearLayout>