<?xml version="1.0" encoding="utf-8"?><!--
https://developer.android.com/design/ui/tv/guides/foundations/navigation-on-tv
1. Don't display a back button

https://developer.android.com/design/ui/tv/guides/styles/layouts
Two-pane layout
or https://developer.android.com/design/ui/tv/guides/styles/layouts#layout-templates

cast:
https://developer.android.com/design/ui/tv/guides/components/cards#1:1

https://developer.android.com/design/ui/tv/samples/jet-fit
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/result_root"
    style="@style/DarkFragment"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/primaryBlackBackground">

    <FrameLayout
        android:id="@+id/background_poster_holder"
        android:layout_width="match_parent"
        android:layout_height="275dp"
        android:visibility="visible">

        <com.lagradost.cloudstream3.utils.PercentageCropImageView
            android:id="@+id/background_poster"
            android:layout_width="match_parent"
            android:layout_height="275dp"
            android:layout_gravity="center"
            android:alpha="0.8"
            android:scaleType="matrix"
            tools:src="@drawable/profile_bg_dark_blue" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_gravity="bottom"
            android:src="@drawable/background_shadow" />
    </FrameLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/result_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:orientation="vertical"
        app:shimmer_auto_start="true"
        app:shimmer_base_alpha="0.2"
        app:shimmer_duration="@integer/loading_time"
        app:shimmer_highlight_alpha="0.3"
        tools:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/result_padding"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/loading_margin"
                android:orientation="horizontal">

                <include layout="@layout/loading_poster" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/loading_margin"
                    android:layout_marginEnd="@dimen/loading_margin"
                    android:orientation="vertical">

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line" />

                    <include layout="@layout/loading_line_short" />
                </LinearLayout>
            </LinearLayout>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="20dp"
                tools:ignore="ContentDescription" />

            <include layout="@layout/loading_episode" />

            <include layout="@layout/loading_episode" />

            <include layout="@layout/loading_episode" />
        </LinearLayout>
    </com.facebook.shimmer.ShimmerFrameLayout>

    <LinearLayout
        android:id="@+id/result_loading_error"

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="gone">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/result_reload_connectionerror"
            style="@style/WhiteButton"

            android:layout_width="wrap_content"

            android:layout_gravity="center"
            android:layout_margin="5dp"
            android:minWidth="200dp"
            android:text="@string/reload_error"
            app:icon="@drawable/ic_baseline_autorenew_24" />

        <!--
        <com.google.android.material.button.MaterialButton
            android:id="@+id/result_reload_connection_open_in_browser"
            style="@style/BlackButton"

            android:layout_width="wrap_content"

            android:layout_gravity="center"
            android:layout_margin="5dp"
            android:minWidth="200dp"
            android:text="@string/result_open_in_browser"
            app:icon="@drawable/ic_baseline_public_24" />
            -->

        <TextView
            android:id="@+id/result_error_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="5dp"
            android:gravity="center"
            android:textColor="?attr/textColor" />
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/result_finish_loading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="@dimen/result_padding"
            android:paddingEnd="@dimen/result_padding">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="225dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/result_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:gravity="center_vertical"
                    android:singleLine="true"
                    android:textColor="?attr/textColor"
                    android:textSize="25sp"
                    android:textStyle="bold"
                    tools:text="The Perfect Run The Perfect Run" />

                <TextView
                    android:id="@+id/result_episodes_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="?attr/textColor"
                    android:textSize="17sp"
                    android:textStyle="normal"
                    android:visibility="gone"
                    tools:text="8 Episodes"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/result_next_airing_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/result_next_airing"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:gravity="start"
                        android:textColor="?attr/grayTextColor"
                        android:textSize="17sp"
                        android:textStyle="normal"
                        tools:text="Season 2 Episode 1022 will be released in" />

                    <TextView
                        android:id="@+id/result_next_airing_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:textColor="?attr/textColor"
                        android:textSize="17sp"
                        android:textStyle="normal"
                        tools:text="5d 3h 30m" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_resume_progress_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <!--https://github.com/material-components/material-components-android/blob/master/docs/components/ProgressIndicator.md-->
                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/result_resume_series_progress"

                        style="@style/RoundProgressbar"

                        android:layout_width="100dp"
                        android:layout_height="20dp"
                        android:layout_gravity="end|center_vertical"
                        android:layout_weight="1"
                        android:indeterminate="false"
                        android:max="100"
                        android:paddingEnd="10dp"
                        android:progress="0"

                        android:scrollbarStyle="outsideInset"
                        tools:ignore="RtlSymmetry"
                        tools:progress="25"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/result_resume_series_progress_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLines="1"
                        android:textColor="?attr/grayTextColor"
                        tools:ignore="RtlSymmetry"
                        tools:text="69m remaining" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/result_play_parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="5dp"
                android:descendantFocusability="afterDescendants"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/result_play_movie"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_play_movie_button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_play_movie_button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/ic_baseline_play_arrow_24"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_play_movie_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/movies_singular" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_play_series"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_play_series_button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_play_series_button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/ic_baseline_play_arrow_24"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_play_series_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/episode" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_resume_series"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_resume_series_button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_resume_series_button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/ic_baseline_resume_arrow2"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_resume_series_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/resume" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_play_trailer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_play_trailer_button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_play_trailer_button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/ic_baseline_film_roll_24"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_play_trailer_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/play_trailer_button" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_bookmark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_bookmark_Button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_bookmark_Button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/outline_bookmark_add_24"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_bookmark_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/type_none" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_favorite"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_favorite_Button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_favorite_Button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/ic_baseline_favorite_border_24"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_favorite_Text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/favorite" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_subscribe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_subscribe_Button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_subscribe_Button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/baseline_notifications_none_24"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_subscribe_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/action_subscribe" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_search"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_search_Button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusUp="@id/result_search_Button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/search_icon"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_search_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/title_search" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/result_episodes_show"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/result_episodes_show_button"
                        style="@style/ResultSmallButtonTV"
                        android:focusable="true"
                        android:nextFocusRight="@id/redirect_to_episodes"
                        android:nextFocusUp="@id/result_episodes_show_button"
                        android:nextFocusDown="@id/result_description"
                        android:tag="@string/tv_no_focus_tag"
                        app:icon="@drawable/ic_baseline_sort_24"
                        app:iconPadding="0dp" />

                    <TextView
                        android:id="@+id/result_episodes_show_text"
                        style="@style/ResultMarqueeButtonText"
                        android:text="@string/episodes" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:baselineAligned="false"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/right_layout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/linearLayout2"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UselessParent">

                    <com.lagradost.cloudstream3.widget.FlowLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        app:itemSpacing="10dp">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/result_meta_site"
                            style="@style/SmallWhiteButton"
                            android:layout_gravity="center_vertical"
                            tools:text="Gogoanime" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/result_meta_content_rating"
                            style="@style/SmallWhiteButton"
                            android:layout_gravity="center_vertical"
                            android:focusable="false"
                            tools:text="PG-13" />

                        <TextView
                            android:id="@+id/result_meta_type"
                            style="@style/ResultInfoText"
                            tools:text="Movie" />

                        <TextView
                            android:id="@+id/result_meta_year"
                            style="@style/ResultInfoText"
                            tools:text="2022" />

                        <TextView
                            android:id="@+id/result_meta_rating"
                            style="@style/ResultInfoText"
                            tools:text="Rated: 8.5/10.0" />

                        <TextView
                            android:id="@+id/result_meta_status"
                            style="@style/ResultInfoText"
                            tools:text="Ongoing" />

                        <TextView
                            android:id="@+id/result_meta_duration"
                            style="@style/ResultInfoText"
                            tools:text="121min" />
                    </com.lagradost.cloudstream3.widget.FlowLayout>


                    <TextView
                        android:id="@+id/result_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fadingEdgeLength="30dp"
                        android:focusable="true"
                        android:foreground="@drawable/outline_drawable"
                        android:maxLines="7"
                        android:nextFocusUp="@id/result_play_parent"
                        android:nextFocusDown="@id/result_cast_items"
                        android:padding="5dp"
                        android:requiresFadingEdge="vertical"
                        android:textColor="?attr/textColor"
                        android:textSize="16sp"
                        tools:text="Ryan Quicksave Romano is an eccentric adventurer with a strange power: he can create a save-point in time and redo his life whenever he dies. Arriving in New Rome, the glitzy capital of sin of a rebuilding Europe, he finds the city torn between mega-corporations, sponsored heroes, superpowered criminals, and true monsters. It's a time of chaos, where potions can grant the power to rule the world and dangers lurk everywhere. " />

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/result_tag"
                        style="@style/ChipParent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/result_cast_items"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:descendantFocusability="afterDescendants"

                android:fadingEdge="horizontal"
                android:nextFocusUp="@id/result_description"
                android:nextFocusDown="@id/result_recommendations_list"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:requiresFadingEdge="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/cast_item"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/result_tv_coming_soon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingTop="50dp"
                android:text="@string/coming_soon"
                android:textColor="?attr/textColor"
                android:textSize="20sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/result_recommendations_holder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:descendantFocusability="afterDescendants"
                android:orientation="horizontal"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/result_recommendations_filter_selection"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:descendantFocusability="afterDescendants"
                    android:nextFocusUp="@id/result_cast_items"
                    android:nextFocusDown="@id/result_recommendations_list"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/result_selection" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:text="@string/recommended"
                    android:textColor="?attr/textColor"
                    android:textSize="17sp" />
            </LinearLayout>

            <com.lagradost.cloudstream3.ui.AutofitRecyclerView
                android:id="@+id/result_recommendations_list"
                android:layout_width="match_parent"

                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:descendantFocusability="afterDescendants"
                android:nextFocusUp="@id/result_cast_items"
                android:orientation="vertical"
                app:spanCount="8"
                tools:listitem="@layout/search_result_grid" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/episodes_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:visibility="gone"
        tools:visibility="invisible">


        <!--
         These two shadow spaces are used to create a view which always x% bigger
         than the episode_holder_tv. This is required for creating a consistent shadow.
        -->
        <Space
            android:id="@+id/shadow_space_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@+id/episode_holder_tv"
            app:layout_constraintStart_toStartOf="@+id/episode_holder_tv" />

        <!--
        The dimension ratio should and episodes_shadow centerX should add up to
        100% for the best results. For example (100:80 + 0.2) or (100:70 + 0.3).
        Bigger centerX => Larger fade distance.
         -->
        <Space
            android:id="@+id/shadow_space_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="100:80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/shadow_space_1" />

        <ImageView
            android:id="@+id/episodes_shadow_background"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:clickable="false"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:importantForAccessibility="no"
            android:src="@drawable/episodes_shadow"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/shadow_space_2"
            app:layout_constraintTop_toTopOf="parent" />


        <LinearLayout
            android:id="@+id/episode_holder_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:orientation="horizontal"
            android:paddingStart="@dimen/result_padding"
            android:paddingEnd="@dimen/result_padding"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlHardcoded"
            tools:visibility="visible">

            <com.lagradost.cloudstream3.ui.MaxRecyclerView
                android:id="@+id/result_dub_selection"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:descendantFocusability="afterDescendants"
                android:nextFocusLeft="@id/result_episodes_show"
                android:nextFocusRight="@id/result_season_selection"
                android:orientation="vertical"
                android:paddingVertical="@dimen/result_padding"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/result_selection"
                tools:visibility="gone" />

            <com.lagradost.cloudstream3.ui.MaxRecyclerView
                android:id="@+id/result_season_selection"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:descendantFocusability="afterDescendants"
                android:nextFocusLeft="@id/result_dub_selection"
                android:nextFocusRight="@id/result_range_selection"
                android:orientation="vertical"
                android:paddingVertical="@dimen/result_padding"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/result_selection"
                tools:visibility="gone" />

            <com.lagradost.cloudstream3.ui.MaxRecyclerView
                android:id="@+id/result_range_selection"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:descendantFocusability="afterDescendants"
                android:nextFocusLeft="@id/result_season_selection"
                android:nextFocusRight="@id/result_episodes"
                android:orientation="vertical"
                android:paddingVertical="@dimen/result_padding"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/result_selection"
                tools:visibility="visible" />


            <!--<androidx.core.widget.ContentLoadingProgressBar
                    android:id="@+id/result_episode_loading"

                    style="@style/Widget.AppCompat.ProgressBar"
                    android:layout_gravity="center"
                    android:layout_width="50dp"
                    android:layout_height="50dp" />-->

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/result_episodes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:descendantFocusability="afterDescendants"
                android:nextFocusLeft="@id/result_range_selection"
                android:orientation="vertical"
                android:paddingVertical="@dimen/result_padding"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/result_episode" />

            <View
                android:id="@+id/temporary_no_focus"
                android:layout_width="1dp"
                android:layout_height="1dp"
                android:focusable="false"
                android:nextFocusLeft="@id/temporary_no_focus"
                android:nextFocusRight="@id/temporary_no_focus"
                android:nextFocusUp="@id/temporary_no_focus"
                android:nextFocusDown="@id/temporary_no_focus" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:orientation="vertical"
            android:paddingStart="@dimen/result_padding"
            android:paddingEnd="@dimen/result_padding"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="15dp"
                android:orientation="horizontal"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:layout_marginBottom="10dp"
                        android:orientation="horizontal">

                        <!--
                        <ImageView
                            android:id="@+id/result_back"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:background="?android:attr/selectableItemBackgroundBorderless"
                            android:clickable="true"
                            android:contentDescription="@string/go_back"
                            android:focusable="true"
                            android:gravity="center_vertical"
                            android:nextFocusDown="@id/result_play_movie"
                            android:src="@drawable/ic_baseline_arrow_back_24"
                            app:tint="?attr/white" />-->

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/result_poster_holder"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="10dp"
                            android:visibility="gone"
                            app:cardCornerRadius="@dimen/rounded_image_radius">

                            <ImageView
                                android:id="@+id/result_poster"
                                android:layout_width="100dp"
                                android:layout_height="140dp"
                                android:layout_gravity="bottom"
                                android:contentDescription="@string/result_poster_img_des"
                                android:foreground="@drawable/outline_drawable"
                                android:scaleType="centerCrop"
                                tools:src="@drawable/example_poster" />
                        </androidx.cardview.widget.CardView>

                        <!--
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <com.lagradost.cloudstream3.widget.FlowLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:itemSpacing="10dp">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/result_meta_site"
                                    style="@style/SmallBlackButton"
                                    android:layout_gravity="center_vertical"
                                    tools:text="Gogoanime" />

                                <TextView
                                    android:id="@+id/result_meta_type"
                                    style="@style/ResultInfoText"
                                    tools:text="Movie" />

                                <TextView
                                    android:id="@+id/result_meta_year"
                                    style="@style/ResultInfoText"
                                    tools:text="2022" />

                                <TextView
                                    android:id="@+id/result_meta_rating"
                                    style="@style/ResultInfoText"
                                    tools:text="Rated: 8.5/10.0" />

                                <TextView
                                    android:id="@+id/result_meta_status"
                                    style="@style/ResultInfoText"
                                    tools:text="Ongoing" />

                                <TextView
                                    android:id="@+id/result_meta_duration"
                                    style="@style/ResultInfoText"
                                    tools:text="121min" />
                            </com.lagradost.cloudstream3.widget.FlowLayout>

                            <TextView
                                android:id="@+id/result_description"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:fadingEdgeLength="30dp"
                                android:foreground="@drawable/outline_drawable"
                                android:maxLines="7"
                                android:nextFocusUp="@id/result_back"
                                android:nextFocusDown="@id/result_bookmark_button"
                                android:padding="5dp"
                                android:requiresFadingEdge="vertical"
                                android:textColor="?attr/textColor"
                                android:textSize="15sp"
                                tools:text="Ryan Quicksave Romano is an eccentric adventurer with a strange power: he can create a save-point in time and redo his life whenever he dies. Arriving in New Rome, the glitzy capital of sin of a rebuilding Europe, he finds the city torn between mega-corporations, sponsored heroes, superpowered criminals, and true monsters. It's a time of chaos, where potions can grant the power to rule the world and dangers lurk everywhere. " />
                        </LinearLayout>
                        -->
                        <!--
                                                    This has half margin and half padding to make TV focus on description look better.
                                                    The focus outline now settles between the poster and text.
                                                    -->
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/result_cast_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="?attr/grayTextColor"
                android:textSize="15sp"
                tools:text="Cast: Joe Ligma" />

            <TextView
                android:id="@+id/result_vpn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="?attr/grayTextColor"
                android:textSize="15sp"
                tools:text="@string/vpn_torrent" />

            <TextView
                android:id="@+id/result_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:textColor="?attr/grayTextColor"
                android:textSize="15sp"
                tools:text="@string/provider_info_meta" />

            <TextView
                android:id="@+id/result_no_episodes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:textColor="?attr/grayTextColor"
                android:textSize="15sp"
                tools:text="@string/no_episodes_found" />


            <TextView
                android:id="@+id/result_coming_soon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingTop="50dp"
                android:text="@string/coming_soon"
                android:textColor="?attr/textColor"
                android:textSize="20sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/result_data_holder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/result_add_sync"
                    style="@style/WhiteButton"
                    android:layout_width="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="0dp"

                    android:layout_marginBottom="10dp"
                    android:text="@string/add_sync"
                    android:visibility="gone"
                    app:icon="@drawable/ic_baseline_add_24" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="0"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/result_movie_parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:animateLayoutChanges="true"
                    android:orientation="vertical"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/series_holder"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <View
                            android:id="@+id/redirect_to_episodes"
                            android:layout_width="1dp"
                            android:layout_height="1dp"
                            android:focusable="true"
                            android:focusableInTouchMode="true" />

                        <View
                            android:id="@+id/redirect_to_play"
                            android:layout_width="1dp"
                            android:layout_height="1dp"
                            android:focusable="true"
                            android:focusableInTouchMode="true" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</FrameLayout>
