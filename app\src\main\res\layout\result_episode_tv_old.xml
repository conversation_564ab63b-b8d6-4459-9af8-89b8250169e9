<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    android:id="@+id/episode_holder"
    android:layout_width="wrap_content"
    android:layout_height="50dp"

    android:layout_marginBottom="5dp"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="@dimen/rounded_image_radius"
    app:cardElevation="0dp">

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/episode_progress"
        style="@android:style/Widget.Material.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="-1.5dp"
        android:progressBackgroundTint="?attr/colorPrimary"
        android:progressTint="?attr/colorPrimary"
        tools:progress="50" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">

        <!--marquee_forever-->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/episode_filler"
            style="@style/SmallWhiteButton"
            android:layout_gravity="center"
            android:layout_marginEnd="10dp"
            android:gravity="center"
            android:text="@string/filler"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/episode_text"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:ellipsize="marquee"

            android:gravity="center"
            android:marqueeRepeatLimit="0"

            android:scrollHorizontally="true"
            android:singleLine="true"

            android:textColor="?attr/textColor"
            tools:text="Episode 1" />
    </LinearLayout>
</androidx.cardview.widget.CardView>