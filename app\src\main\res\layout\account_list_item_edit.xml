<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_view"
    android:layout_width="110dp"
    android:layout_height="110dp"
    android:animateLayoutChanges="true"
    android:backgroundTint="?attr/primaryGrayBackground"
    android:foreground="?attr/selectableItemBackground"
    android:layout_margin="10dp"
    android:focusable="true"
    app:cardCornerRadius="@dimen/rounded_image_radius"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintDimensionRatio="1"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintHeight_percent="0.4"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <ImageView
        android:id="@+id/account_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.1"
        android:scaleType="centerCrop" />

    <View
        android:id="@+id/outline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/outline_card"
        android:visibility="gone"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/lock_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="top|end"
        android:layout_margin="4dp"
        android:src="@drawable/video_locked"
        android:visibility="gone"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/pencil_icon"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_gravity="top|start"
        android:src="@drawable/ic_baseline_edit_24" />

    <TextView
        android:id="@+id/account_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:padding="10dp"
        android:textSize="16sp" />

</androidx.cardview.widget.CardView>