<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/subs_root"
        android:background="?attr/primaryBlackBackground">

    <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <TextView
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:textStyle="bold"
                android:text="@string/chromecast_subtitles_settings"
                android:textSize="20sp"
                android:textColor="?attr/textColor"
                android:layout_width="match_parent"
                android:layout_rowWeight="1"
                android:layout_height="wrap_content" />

        <FrameLayout
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="75sp">

            <ImageView
                    android:scaleType="centerCrop"
                    android:src="@drawable/subtitles_preview_background"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:contentDescription="@string/preview_background_img_des" />

            <androidx.media3.ui.SubtitleView
                    android:id="@+id/subtitle_text"

                    android:layout_width="match_parent"
                    android:layout_gravity="center"
                    android:foregroundGravity="center"
                    android:layout_height="match_parent" />
        </FrameLayout>

        <TextView
                android:nextFocusDown="@id/subs_font_size"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/cancel_btt"

                android:id="@+id/subs_font"
                android:text="@string/subs_font"
                style="@style/SettingsItem" />

        <TextView
                android:nextFocusUp="@id/subs_font"
                android:nextFocusDown="@id/subs_text_color"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/cancel_btt"

                android:id="@+id/subs_font_size"
                android:text="@string/subs_font_size"
                style="@style/SettingsItem" />

        <TextView
                android:nextFocusUp="@id/subs_font_size"
                android:nextFocusDown="@id/subs_outline_color"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/cancel_btt"

                android:id="@+id/subs_text_color"
                android:text="@string/subs_text_color"
                style="@style/SettingsItem" />

        <TextView
                android:nextFocusUp="@id/subs_text_color"
                android:nextFocusDown="@id/subs_background_color"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/cancel_btt"

                android:id="@+id/subs_outline_color"
                android:text="@string/subs_outline_color"
                style="@style/SettingsItem" />

        <TextView
                android:nextFocusUp="@id/subs_outline_color"
                android:nextFocusDown="@id/subs_window_color"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/cancel_btt"

                android:id="@+id/subs_background_color"
                android:text="@string/subs_background_color"
                style="@style/SettingsItem" />

        <TextView
                android:visibility="gone"
                android:nextFocusUp="@id/subs_background_color"
                android:nextFocusDown="@id/subs_edge_type"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/cancel_btt"

                android:id="@+id/subs_window_color"
                android:text="@string/subs_window_color"
                style="@style/SettingsItem" />

        <TextView
                android:nextFocusUp="@id/subs_window_color"
                android:nextFocusDown="@id/apply_btt"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/cancel_btt"

                android:id="@+id/subs_edge_type"
                android:text="@string/subs_edge_type"
                style="@style/SettingsItem" />

        <TextView
                android:gravity="center"
                android:text="@string/subs_hold_to_reset_to_default"
                android:textSize="14sp"

                android:textColor="?attr/textColor"
                android:layout_width="match_parent"
                android:layout_rowWeight="1"
                android:layout_height="wrap_content" />

        <LinearLayout
                android:orientation="horizontal"
                android:layout_gravity="bottom"
                android:gravity="bottom|end"
                android:layout_width="match_parent"
                android:layout_height="60dp">

            <com.google.android.material.button.MaterialButton
                    android:nextFocusUp="@id/subs_edge_type"
                    android:nextFocusRight="@id/cancel_btt"
                    style="@style/WhiteButton"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="visible"
                    android:text="@string/sort_apply"
                    android:id="@+id/apply_btt"
                    android:layout_width="wrap_content">

                <requestFocus />
            </com.google.android.material.button.MaterialButton>

            <com.google.android.material.button.MaterialButton
                    android:nextFocusUp="@id/subs_edge_type"
                    android:nextFocusLeft="@id/apply_btt"
                    style="@style/BlackButton"
                    android:layout_gravity="center_vertical|end"
                    android:text="@string/sort_cancel"
                    android:id="@+id/cancel_btt"
                    android:layout_width="wrap_content" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>