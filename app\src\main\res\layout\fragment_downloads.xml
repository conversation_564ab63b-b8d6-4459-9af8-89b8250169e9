<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/download_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.download.DownloadFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/primaryGrayBackground"
        tools:layout_height="100dp">
        <!--
        For Scroll add to LinearLayout
        app:layout_scrollFlags="scroll|enterAlways"
        -->
        <LinearLayout
            android:id="@+id/download_delete_appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="?attr/colorPrimary"
            android:padding="8dp"
            android:visibility="gone">

            <ImageButton
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:src="@drawable/ic_baseline_close_24"
                android:contentDescription="@string/cancel"
                android:padding="8dp"
                android:layout_gravity="center_vertical"
                android:nextFocusLeft="@id/nav_rail_view"
                app:tint="@android:color/white" />

            <Button
                android:id="@+id/btnDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:text="@string/delete"
                android:textColor="@android:color/white"
                android:layout_gravity="center_vertical" />

            <TextView
                android:id="@+id/selectItemsText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:text="@string/downloads_delete_select"
                android:textColor="@android:color/white"
                android:layout_gravity="center_vertical"
                android:visibility="gone" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btnToggleAll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:text="@string/select_all"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:nextFocusDown="@id/download_list" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/download_storage_appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/download_storage_text"
                android:textColor="?attr/textColor" />

            <androidx.cardview.widget.CardView
                android:layout_width="fill_parent"
                android:layout_height="12dp"
                android:layout_marginBottom="5dp"
                android:elevation="0dp"
                app:cardCornerRadius="@dimen/storage_radius"
                app:cardElevation="0dp"
                app:cardMaxElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <View
                        android:id="@+id/download_used"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"
                        android:background="@drawable/storage_bar_left" />

                    <View
                        android:id="@+id/download_app"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.10"
                        android:background="@drawable/storage_bar_mid" />

                    <View
                        android:id="@+id/download_free"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.10"
                        android:background="@drawable/storage_bar_right" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <View
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/storage_bar_left_box" />

                <TextView
                    android:id="@+id/download_used_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="?attr/textColor"
                    android:textSize="12sp"
                    tools:text="Used • 30.58GB" />

                <View
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="5dp"
                    android:background="@drawable/storage_bar_mid_box" />

                <TextView
                    android:id="@+id/download_app_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="?attr/textColor"
                    android:textSize="12sp"
                    tools:text="App • 30.58GB" />

                <View
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="5dp"
                    android:background="@drawable/storage_bar_right_box" />

                <TextView
                    android:id="@+id/download_free_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="?attr/textColor"
                    android:textSize="12sp"
                    tools:text="Free • 30.58GB" />
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/download_list"
        android:layout_width="match_parent"
        android:paddingBottom="100dp"
        android:clipToPadding="false"
        android:layout_height="match_parent"
        android:background="?attr/primaryBlackBackground"
        android:descendantFocusability="afterDescendants"
        android:nextFocusLeft="@id/nav_rail_view"
        android:tag="@string/tv_no_focus_tag"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:listitem="@layout/download_header_episode" />

    <TextView
        android:id="@+id/text_no_downloads"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="30dp"
        android:text="@string/downloads_empty"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <!--
    <ProgressBar
        android:visibility="visible"
        tools:visibility="gone"
        android:id="@+id/download_loading"
        android:layout_gravity="center"
        android:layout_width="50dp" android:layout_height="50dp">
    </ProgressBar>-->

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/download_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:paddingTop="40dp"
        app:shimmer_auto_start="true"
        app:shimmer_base_alpha="0.2"
        app:shimmer_duration="@integer/loading_time"
        app:shimmer_highlight_alpha="0.3"
        tools:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/loading_margin"
            android:layout_marginEnd="@dimen/loading_margin"
            android:orientation="vertical">

            <include layout="@layout/loading_downloads" />
            <include layout="@layout/loading_downloads" />
            <include layout="@layout/loading_downloads" />
            <include layout="@layout/loading_downloads" />
            <include layout="@layout/loading_downloads" />
            <include layout="@layout/loading_downloads" />
        </LinearLayout>
    </com.facebook.shimmer.ShimmerFrameLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="bottom|end">

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/open_local_video_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="?attr/floatingActionButtonSmallStyle"
            android:backgroundTint="?attr/primaryGrayBackground"
            android:src="@drawable/netflix_play"
            android:layout_marginEnd="16dp"
            android:tooltipText="@string/open_local_video"
            android:layout_gravity="bottom|end"
            android:contentDescription="@string/open_local_video" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/download_stream_button"
            style="@style/ExtendedFloatingActionButton"
            android:text="@string/stream"
            android:textColor="?attr/textColor"
            app:icon="@drawable/ic_network_stream"
            android:contentDescription="@string/stream" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>