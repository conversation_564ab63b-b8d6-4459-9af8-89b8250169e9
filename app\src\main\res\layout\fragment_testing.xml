<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="?attr/primaryBlackBackground"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/provider_test_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/settings_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/primaryGrayBackground"
            android:paddingTop="@dimen/navbar_height"
            app:layout_scrollFlags="scroll|enterAlways"
            app:navigationIconTint="?attr/iconColor"
            app:titleTextColor="?attr/textColor"
            tools:title="@string/category_provider_test">

        </com.google.android.material.appbar.MaterialToolbar>

        <com.lagradost.cloudstream3.ui.settings.testing.TestView
            android:id="@+id/provider_test"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nextFocusDown="@id/provider_test_recycler_view"
            app:header_text="@string/category_provider_test"
            app:layout_scrollFlags="scroll|enterAlways" />
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/provider_test_recycler_view"
            android:clipToPadding="false"
            android:paddingBottom="60dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/provider_test_item" />

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>