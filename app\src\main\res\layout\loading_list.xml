<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:orientation="vertical"
    android:paddingTop="@dimen/loading_margin"
    android:paddingBottom="@dimen/loading_margin">

    <include layout="@layout/loading_line_short" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <include layout="@layout/loading_poster" />

        <View
            android:layout_width="@dimen/loading_margin"
            android:layout_height="wrap_content" />

        <include layout="@layout/loading_poster" />

        <View
            android:layout_width="@dimen/loading_margin"
            android:layout_height="wrap_content" />

        <include layout="@layout/loading_poster" />

        <View
            android:layout_width="@dimen/loading_margin"
            android:layout_height="wrap_content" />

        <include layout="@layout/loading_poster" />

        <View
            android:layout_width="@dimen/loading_margin"
            android:layout_height="wrap_content" />

        <include layout="@layout/loading_poster" />

        <View
            android:layout_width="@dimen/loading_margin"
            android:layout_height="wrap_content" />

        <include layout="@layout/loading_poster" />

        <View
            android:layout_width="@dimen/loading_margin"
            android:layout_height="match_parent" />

        <include layout="@layout/loading_poster" />

        <View
            android:layout_width="@dimen/loading_margin"
            android:layout_height="wrap_content" />

        <include layout="@layout/loading_poster" />
    </LinearLayout>

</LinearLayout>

