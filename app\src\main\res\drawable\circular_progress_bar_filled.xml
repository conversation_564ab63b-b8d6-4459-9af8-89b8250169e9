<?xml version="1.0" encoding="utf-8"?>
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
        android:fromDegrees="270"
        android:toDegrees="270">
    <shape android:shape="oval" />
    <shape
            android:innerRadiusRatio="100"
            android:shape="ring"
            android:thickness="11dp"
            android:useLevel="true"><!-- this line fixes the issue for lollipop api 21 -->

        <gradient
                android:angle="0"
                android:endColor="?attr/white"
                android:startColor="?attr/white"
                android:type="sweep"
                android:useLevel="false" />
    </shape>
</rotate>