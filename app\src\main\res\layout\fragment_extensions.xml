<?xml version="1.0" encoding="utf-8"?>

<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/extensions_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/standard_toolbar" />

    <androidx.recyclerview.widget.RecyclerView
        android:clipToPadding="false"
        android:id="@+id/repo_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:nextFocusUp="@id/settings_toolbar"
        android:nextFocusDown="@id/plugin_storage_appbar"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:layout_constraintBottom_toTopOf="@id/download_storage_appbar"
        app:layout_constraintTop_toTopOf="parent"
        tools:listitem="@layout/repository_item"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/blank_repo_screen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="20dp"
        android:gravity="center"
        android:orientation="vertical"
        tools:visibility="gone">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_baseline_extension_24" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="@string/blank_repo_message"
            android:textSize="16sp" />

        <!--        <com.google.android.material.button.MaterialButton-->
        <!--            android:id="@+id/list_repositories"-->
        <!--            style="@style/WhiteButton"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:nextFocusDown="@id/add_repo_button"-->
        <!--            android:text="@string/view_public_repositories_button" />-->
    </LinearLayout>

    <LinearLayout
        android:id="@+id/plugin_storage_appbar"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_gravity="bottom"
        android:background="?attr/primaryGrayBackground"
        android:focusable="true"
        android:foreground="@drawable/outline_drawable"
        android:nextFocusRight="@id/add_repo_button_imageview"
        android:nextFocusUp="@id/repo_recycler_view"
        android:orientation="horizontal"
        android:padding="10dp"

        android:visibility="visible">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"

            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/extensions"
                android:textColor="?attr/textColor" />

            <androidx.cardview.widget.CardView
                android:layout_width="fill_parent"
                android:layout_height="12dp"
                android:layout_marginBottom="5dp"
                android:elevation="0dp"
                app:cardCornerRadius="@dimen/storage_radius"
                app:cardElevation="0dp"
                app:cardMaxElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <View
                        android:id="@+id/plugin_download"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/storage_bar_left"
                        tools:layout_weight="0.5" />

                    <View
                        android:id="@+id/plugin_disabled"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/storage_bar_mid"
                        tools:layout_weight="0.10" />

                    <View
                        android:id="@+id/plugin_not_downloaded"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/storage_bar_right"
                        tools:layout_weight="0.10" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <View
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/storage_bar_left_box" />

                <TextView
                    android:id="@+id/plugin_download_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="?attr/textColor"
                    android:textSize="12sp"
                    tools:text="Downloaded: 7" />

                <View
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="5dp"
                    android:background="@drawable/storage_bar_mid_box" />

                <TextView
                    android:id="@+id/plugin_disabled_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="?attr/textColor"
                    android:textSize="12sp"
                    tools:text="Disabled: 3" />

                <View
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="5dp"
                    android:background="@drawable/storage_bar_right_box" />

                <TextView
                    android:id="@+id/plugin_not_downloaded_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="?attr/textColor"
                    android:textSize="12sp"
                    tools:text="Not downloaded 3" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/add_repo_button_imageview_holder"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:layout_gravity="center"
                android:text="@string/add_repository"
                android:textColor="?attr/textColor" />

            <ImageView
                android:id="@+id/add_repo_button_imageview"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"

                android:background="@drawable/outline_drawable"
                android:contentDescription="@string/add_repository"
                android:focusable="true"
                android:nextFocusLeft="@id/plugin_storage_appbar"
                android:nextFocusUp="@id/repo_recycler_view"

                android:src="@drawable/ic_baseline_add_24"
                app:tint="?attr/textColor" />
        </LinearLayout>

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/add_repo_button"
        style="@style/ExtendedFloatingActionButton"
        android:foreground="@drawable/outline_drawable"
        android:nextFocusDown="@id/plugin_storage_appbar"
        android:text="@string/add_repository"
        android:textColor="?attr/textColor"
        android:translationY="-80dp"
        app:icon="@drawable/ic_baseline_add_24"
        tools:ignore="ContentDescription" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>

