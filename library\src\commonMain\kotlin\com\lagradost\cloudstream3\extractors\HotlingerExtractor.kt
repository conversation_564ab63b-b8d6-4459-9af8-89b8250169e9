// ! Bu araç @keyiflerolsun tarafından | @KekikAkademi için yazılmıştır.

package com.lagradost.cloudstream3.extractors

class Hotlinger : ContentX() {
    override var name    = "Hotlinger"
    override var mainUrl = "https://hotlinger.com"
}

class FourCX : ContentX() {
    override var name    = "FourCX"
    override var mainUrl = "https://four.contentx.me"
}

class PlayRu : ContentX() {
    override var name    = "PlayRu"
    override var mainUrl = "https://playru.net"
}

class FourPlayRu : ContentX() {
    override var name    = "FourPlayRu"
    override var mainUrl = "https://four.playru.net"
}

class Pichive : ContentX() {
    override var name    = "Pichive"
    override var mainUrl = "https://pichive.online"
}

class FourPichive : ContentX() {
    override var name    = "FourPichive"
    override var mainUrl = "https://four.pichive.online"
}
