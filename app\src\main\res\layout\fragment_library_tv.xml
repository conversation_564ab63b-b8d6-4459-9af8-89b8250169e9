<?xml version="1.0" encoding="utf-8"?>

<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/library_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/empty_list_textview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="30dp"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/search_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/primaryGrayBackground">

        <LinearLayout
            android:id="@+id/search_status_bar_padding"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_scrollFlags="scroll|enterAlways">

            <ImageView
                android:id="@+id/provider_selector"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="end|center_vertical"
                android:layout_marginStart="10dp"
                android:padding="2dp"
                android:background="?selectableItemBackgroundBorderless"
                android:contentDescription="@string/change_providers_img_des"
                android:focusable="true"
                android:nextFocusLeft="@id/nav_rail_view"
                android:nextFocusRight="@id/library_sort"
                android:tag="@string/tv_no_focus_tag"
                android:src="@drawable/ic_baseline_extension_24"
                app:tint="?attr/textColor" />

            <ImageView
                android:id="@+id/library_sort"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="end|center_vertical"
                android:layout_marginStart="10dp"
                android:background="?selectableItemBackgroundBorderless"
                android:contentDescription="@string/change_providers_img_des"
                android:focusable="true"
                android:nextFocusLeft="@id/provider_selector"
                android:nextFocusRight="@id/list_selector"
                android:tag="@string/tv_no_focus_tag"
                android:src="@drawable/ic_baseline_sort_24"
                app:tint="?attr/textColor" />

            <ImageView
                android:id="@+id/list_selector"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="end|center_vertical"
                android:padding="1dp"
                android:layout_marginStart="10dp"
                android:background="?selectableItemBackgroundBorderless"
                android:contentDescription="@string/change_providers_img_des"
                android:focusable="true"
                android:nextFocusLeft="@id/library_sort"
                android:nextFocusRight="@id/main_search"
                android:tag="@string/tv_no_focus_tag"
                android:src="@drawable/ic_baseline_filter_list_24"
                app:tint="?attr/textColor" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_margin="10dp"
                android:background="@drawable/search_background"
                android:visibility="visible"
                app:layout_scrollFlags="scroll|enterAlways">

                <androidx.appcompat.widget.SearchView
                    android:id="@+id/main_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"

                    android:iconifiedByDefault="false"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:focusable="true"
                    android:tag="tv_no_focus_tag"
                    android:nextFocusLeft="@id/list_selector"
                    android:nextFocusDown="@id/search_result_root"
                    android:paddingStart="-10dp"
                    app:iconifiedByDefault="false"
                    app:queryBackground="@color/transparent"
                    app:queryHint="@string/search_hint"
                    app:searchIcon="@drawable/search_icon"
                    app:closeIcon="@drawable/ic_baseline_close_24"
                    tools:ignore="RtlSymmetry">

                </androidx.appcompat.widget.SearchView>
            </FrameLayout>
        </LinearLayout>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/library_tab_layout"
            style="@style/Theme.Widget.Tabs"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="top"
            android:nextFocusDown="@id/search_result_root"
            android:background="?attr/primaryGrayBackground"
            android:paddingHorizontal="5dp"
            android:focusable="true"
            app:tabGravity="center"
            app:tabIndicator="@drawable/indicator_background"
            app:tabIndicatorColor="?attr/white"
            app:tabIndicatorGravity="center"
            app:tabIndicatorHeight="30dp"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="?attr/primaryBlackBackground"
            app:tabTextAppearance="@style/TabNoCaps"
            app:tabTextColor="?attr/textColor" />
    </com.google.android.material.appbar.AppBarLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewpager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="40dp"
                android:focusable="true"
                android:tag="@string/tv_no_focus_tag"
                tools:listitem="@layout/library_viewpager_page" />

        <LinearLayout
            android:id="@+id/library_loading_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="false"
            android:tag="@string/tv_no_focus_tag"
            android:background="?attr/primaryBlackBackground"
            android:visibility="gone"
            tools:visibility="visible">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/library_loading_shimmer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_margin="2dp"
                app:shimmer_auto_start="true"
                app:shimmer_base_alpha="0.2"
                android:focusable="false"
                android:tag="@string/tv_no_focus_tag"
                app:shimmer_duration="@integer/loading_time"
                app:shimmer_highlight_alpha="0.3">

                <GridView
                    android:id="@+id/gridview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:gravity="center"
                    android:horizontalSpacing="10dp"
                    android:numColumns="3"
                    android:paddingBottom="120dp"
                    android:focusable="false"
                    android:tag="@string/tv_no_focus_tag"
                    android:verticalSpacing="10dp"
                    tools:listitem="@layout/loading_poster_dynamic" />
            </com.facebook.shimmer.ShimmerFrameLayout>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="40dp">
        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/library_random"
            style="@style/ExtendedFloatingActionButton"
            android:layout_gravity="bottom|start"
            android:text="@string/home_random"
            android:textColor="?attr/textColor"
            android:visibility="gone"
            app:icon="@drawable/ic_baseline_play_arrow_24"
            tools:ignore="ContentDescription"
            tools:visibility="visible" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/sort_fab"
            style="@style/ExtendedFloatingActionButton"
            android:text="@string/sort"
            android:textColor="?attr/textColor"
            android:visibility="gone"
            tools:visibility="visible"
            app:icon="@drawable/ic_baseline_sort_24"
            tools:ignore="ContentDescription" />
    </FrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
