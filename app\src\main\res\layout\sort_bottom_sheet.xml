<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:background="@null"
        android:layout_height="match_parent">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:layout_weight="50">

        <TextView
                android:paddingStart="?android:attr/listPreferredItemPaddingStart"
                android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:textStyle="bold"
                android:text="@string/pick_source"
                android:textSize="20sp"
                android:textColor="?attr/textColor"
                android:layout_width="match_parent"
                android:layout_rowWeight="1"
                android:layout_height="wrap_content" />

        <ListView
                android:layout_marginTop="-10dp"
                android:paddingTop="10dp"
                android:id="@+id/sort_providers"
                android:background="?attr/primaryBlackBackground"
                android:requiresFadingEdge="vertical"
                tools:listitem="@layout/sort_bottom_single_choice"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_rowWeight="1" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/sort_subtitles_holder"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:layout_weight="50">

        <TextView
                android:paddingStart="?android:attr/listPreferredItemPaddingStart"
                android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:textStyle="bold"
                android:text="@string/pick_subtitle"
                android:textSize="20sp"
                android:textColor="?attr/textColor"
                android:layout_rowWeight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        <ListView
                android:layout_marginTop="-10dp"
                android:paddingTop="10dp"
                android:id="@+id/sort_subtitles"
                android:background="?attr/primaryBlackBackground"
                android:requiresFadingEdge="vertical"
                tools:listitem="@layout/sort_bottom_single_choice"
                android:layout_width="match_parent"
                android:layout_rowWeight="1"
                android:layout_height="match_parent" />
    </LinearLayout>
</LinearLayout>
