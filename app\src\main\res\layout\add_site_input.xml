<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <TextView
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:id="@+id/text1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:layout_gravity="center_vertical"
            android:layout_rowWeight="1"

            android:paddingStart="?android:attr/listPreferredItemPaddingStart"
            android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
            android:textColor="?attr/textColor"
            android:textSize="20sp"
            android:textStyle="bold"
            android:text="@string/add_site_pref" />

    <TextView
            android:layout_marginBottom="10dp"

            android:id="@+id/text2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:layout_gravity="center_vertical"
            android:layout_rowWeight="1"

            android:paddingStart="?android:attr/listPreferredItemPaddingStart"
            android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
            android:textColor="?attr/grayTextColor"
            android:textSize="15sp"
            tools:text="Gogoanime" />

    <LinearLayout
            android:orientation="vertical"
            android:layout_marginBottom="60dp"
            android:layout_marginHorizontal="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <EditText
                android:textColorHint="?attr/grayTextColor"
                android:hint="@string/example_site_name"
                android:autofillHints="username"
                android:id="@+id/site_name_input"
                android:nextFocusRight="@id/cancel_btt"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusDown="@id/site_url_input"
                android:requiresFadingEdge="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                tools:ignore="LabelFor" />

        <EditText
                android:textColorHint="?attr/grayTextColor"
                android:hint="@string/example_site_url"
                android:id="@+id/site_url_input"
                android:nextFocusRight="@id/cancel_btt"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusUp="@id/site_name_input"
                android:nextFocusDown="@id/site_lang_input"

                android:requiresFadingEdge="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri"
                tools:ignore="LabelFor" />

        <EditText
                android:textColorHint="?attr/grayTextColor"
                android:hint="@string/example_lang_name"
                android:autofillHints="username"
                android:id="@+id/site_lang_input"
                android:nextFocusUp="@id/site_url_input"
                android:nextFocusRight="@id/cancel_btt"
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusDown="@id/apply_btt"
                android:requiresFadingEdge="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                tools:ignore="LabelFor" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/apply_btt_holder"
            android:orientation="horizontal"
            android:layout_gravity="bottom"
            android:gravity="bottom|end"
            android:layout_marginTop="-60dp"
            android:layout_width="match_parent"
            android:layout_height="60dp">

        <com.google.android.material.button.MaterialButton
                style="@style/WhiteButton"
                android:layout_gravity="center_vertical|end"
                android:text="@string/add_site_pref"
                android:id="@+id/apply_btt"
                android:layout_width="wrap_content" />

        <com.google.android.material.button.MaterialButton
                style="@style/BlackButton"
                android:layout_gravity="center_vertical|end"
                android:text="@string/sort_cancel"
                android:id="@+id/cancel_btt"
                android:layout_width="wrap_content" />
    </LinearLayout>
</LinearLayout>