<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:background="?attr/primaryBlackBackground"
        android:layout_height="match_parent">

    <!--
       app:thumbTint="@color/toggle_selector"
       app:trackTint="@color/toggle_selector"-->
    <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/toggle1"

            android:nextFocusLeft="@id/apply_btt"
            android:nextFocusRight="@id/cancel_btt"

            android:layout_marginTop="10dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:textStyle="bold"
            android:textSize="20sp"
            app:useMaterialThemeColors="false"

            android:textColor="?attr/textColor"
            tools:text="Search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <requestFocus />
    </com.google.android.material.switchmaterial.SwitchMaterial>

    <ListView
            android:nextFocusRight="@id/cancel_btt"
            android:nextFocusLeft="@id/apply_btt"

            android:nextFocusUp="@id/toggle1"
            android:nextFocusDown="@id/apply_btt"

            android:id="@+id/listview1"
            android:layout_marginTop="-10dp"
            android:layout_marginBottom="60dp"
            android:paddingTop="10dp"
            android:requiresFadingEdge="vertical"
            tools:listitem="@layout/sort_bottom_single_choice"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_rowWeight="1" />

    <ListView
            android:nextFocusRight="@id/cancel_btt"
            android:nextFocusLeft="@id/apply_btt"

            android:nextFocusUp="@id/toggle1"
            android:nextFocusDown="@id/apply_btt"

            android:id="@+id/listview2"
            android:layout_marginTop="-10dp"
            android:layout_marginBottom="60dp"
            android:paddingTop="10dp"
            android:requiresFadingEdge="vertical"
            tools:listitem="@layout/sort_bottom_single_choice"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_rowWeight="1" />

    <LinearLayout
            android:id="@+id/apply_btt_holder"
            android:orientation="horizontal"
            android:layout_gravity="bottom"
            android:gravity="bottom|end"
            android:layout_marginTop="-60dp"
            android:layout_width="match_parent"
            android:layout_height="60dp">

        <com.google.android.material.button.MaterialButton
                android:nextFocusRight="@id/cancel_btt"
                android:nextFocusLeft="@id/cancel_btt"

                android:id="@+id/apply_btt"
                style="@style/WhiteButton"
                android:layout_gravity="center_vertical|end"
                android:text="@string/sort_apply"
                android:layout_width="wrap_content" />

        <com.google.android.material.button.MaterialButton
                android:nextFocusLeft="@id/apply_btt"
                android:nextFocusRight="@id/apply_btt"

                android:id="@+id/cancel_btt"
                style="@style/BlackButton"
                android:layout_gravity="center_vertical|end"
                android:text="@string/sort_cancel"
                android:layout_width="wrap_content" />
    </LinearLayout>
</LinearLayout>
