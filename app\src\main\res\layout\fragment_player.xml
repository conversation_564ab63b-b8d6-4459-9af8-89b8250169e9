<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/player_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:orientation="horizontal"
    android:screenOrientation="sensorLandscape"
    app:backgroundTint="@android:color/black"
    app:surface_type="texture_view">
    <!--
          app:fastforward_increment="10000"
            app:rewind_increment="10000"-->
    <androidx.media3.ui.PlayerView
        android:id="@+id/player_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        android:focusable="false"
        app:auto_show="true"
        app:backgroundTint="@android:color/black"
        app:controller_layout_id="@layout/player_custom_layout"
        app:hide_on_touch="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:show_timeout="0" />

    <FrameLayout
        android:id="@+id/player_loading_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        android:backgroundTint="@android:color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/overlay_loading_skip_button"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="45dp"
            android:layout_gravity="center"
            android:layout_marginTop="70dp"
            android:backgroundTint="@color/transparent"

            android:text="@string/skip_loading"
            android:textAllCaps="false"
            android:textColor="?attr/textColor"
            android:visibility="gone"
            app:cornerRadius="4dp"
            app:icon="@drawable/ic_baseline_skip_next_24"
            app:iconTint="?attr/textColor"
            app:rippleColor="?attr/colorPrimary"
            tools:visibility="visible" />

        <ProgressBar
            android:id="@+id/main_load"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center" />

        <FrameLayout
            android:id="@+id/video_go_back_holder_holder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center"
                android:contentDescription="@string/go_back_img_des"
                android:src="@drawable/ic_baseline_arrow_back_24"
                app:tint="@android:color/white" />

            <ImageView
                android:id="@+id/player_loading_go_back"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center"
                android:background="@drawable/video_tap_button_always_white"
                android:clickable="true"
                android:contentDescription="@string/go_back_img_des"
                android:focusable="true" />
        </FrameLayout>
    </FrameLayout>

   <!-- <FrameLayout
        android:id="@+id/player_torrent_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/video_torrent_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="start"
            android:textColor="@color/white"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="78% at 18kb/s" />

        <TextView
            android:id="@+id/video_torrent_seeders"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:gravity="start"
            android:textColor="@color/white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/player_video_title"
            tools:text="17 seeders" />
    </FrameLayout>-->
</androidx.constraintlayout.widget.ConstraintLayout>