<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="60dp"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/sort_sources_holder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="50"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/profiles_click_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_rowWeight="1"
                android:layout_marginTop="10dp"
                android:focusable="true"
                android:foreground="@drawable/outline_drawable_forced"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingStart="?android:attr/listPreferredItemPaddingStart"
                    android:paddingTop="10dp"
                    android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
                    android:paddingBottom="10dp"
                    android:text="@string/pick_source"
                    android:textColor="?attr/textColor"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/source_settings_btt"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:drawablePadding="10dp"
                    android:gravity="center"
                    android:minWidth="140dp"
                    android:paddingHorizontal="10dp"
                    android:textColor="?attr/textColor"
                    android:textSize="15sp"
                    app:drawableEndCompat="@drawable/ic_outline_settings_24"
                    tools:text="@string/profile_number" />
            </LinearLayout>

            <ListView
                android:id="@+id/sort_providers"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_rowWeight="1"
                android:background="?attr/primaryBlackBackground"
                android:listSelector="@drawable/outline_drawable_forced"
                android:nextFocusRight="@id/sort_subtitles"
                android:nextFocusUp="@id/profiles_click_settings"
                android:nextFocusDown="@id/apply_btt"
                android:requiresFadingEdge="vertical"
                tools:layout_height="100dp"
                tools:listitem="@layout/sort_bottom_single_choice" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/sort_subtitles_holder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="50"
            android:orientation="vertical">

            <!--   android:id="@+id/subs_settings"                 android:foreground="?android:attr/selectableItemBackgroundBorderless"
-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                tools:ignore="UseCompoundDrawables">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/subtitles_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:paddingVertical="10dp"
                        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
                        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
                        android:text="@string/pick_subtitle"
                        android:textColor="?attr/textColor"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/subtitles_encoding_format"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center"
                        android:layout_toStartOf="@+id/subtitle_settings_btt"
                        android:layout_toEndOf="@id/subtitles_text"
                        android:focusable="true"
                        android:foreground="@drawable/outline_drawable_forced"
                        android:gravity="start"
                        android:maxLines="1"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="10dp"
                        android:textColor="?attr/textColor"
                        android:textSize="15sp"
                        tools:text="Thai (TIS 620-2533/ISO 8859-11)" />

                    <ImageView
                        android:id="@+id/subtitle_settings_btt"
                        android:layout_width="wrap_content"
                        android:layout_height="44dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center"
                        android:contentDescription="@string/subtitles_settings"
                        android:foreground="@drawable/outline_drawable_forced"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="10dp"
                        android:src="@drawable/ic_outline_settings_24" />
                </RelativeLayout>

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:layout_marginTop="0dp"
                    android:layout_marginEnd="10dp"
                    android:contentDescription="@string/home_change_provider_img_des"
                    android:src="@drawable/ic_outline_settings_24"
                    android:visibility="gone" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <ListView
                    android:id="@+id/sort_subtitles"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_rowWeight="1"
                    android:layout_weight="1"
                    android:background="?attr/primaryBlackBackground"
                    android:listSelector="@drawable/outline_drawable_forced"
                    android:nextFocusLeft="@id/sort_providers"
                    android:nextFocusRight="@id/sort_subtitles_options"
                    android:nextFocusUp="@id/subtitles_click_settings"
                    android:nextFocusDown="@id/apply_btt"
                    android:requiresFadingEdge="vertical"
                    tools:layout_height="200dp"
                    tools:listfooter="@layout/sort_bottom_footer_add_choice"
                    tools:listitem="@layout/sort_bottom_single_choice" />

                <ListView
                    android:id="@+id/sort_subtitles_options"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_rowWeight="1"
                    android:layout_weight="1"
                    android:background="?attr/primaryBlackBackground"
                    android:listSelector="@drawable/outline_drawable_forced"
                    android:nextFocusLeft="@id/sort_subtitles"
                    android:nextFocusRight="@id/apply_btt"
                    android:nextFocusUp="@id/subtitle_settings_btt"
                    android:nextFocusDown="@id/apply_btt"
                    android:requiresFadingEdge="vertical"
                    tools:layout_height="200dp"
                    tools:listfooter="@layout/sort_bottom_footer_add_choice"
                    tools:listitem="@layout/sort_bottom_single_choice" />
            </LinearLayout>


        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/apply_btt_holder"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:layout_marginTop="-60dp"
        android:gravity="bottom|end"
        android:orientation="horizontal">


        <com.google.android.material.button.MaterialButton
            android:id="@+id/apply_btt"
            style="@style/WhiteButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/sort_apply" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel_btt"
            style="@style/BlackButton"
            android:layout_width="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:text="@string/sort_cancel" />
    </LinearLayout>
</LinearLayout>
