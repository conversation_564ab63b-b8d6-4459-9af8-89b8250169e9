<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/primaryBlackBackground">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/settings_profile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                tools:visibility="visible">

                <androidx.cardview.widget.CardView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    app:cardCornerRadius="25dp">

                    <ImageView
                        android:id="@+id/settings_profile_pic"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:foreground="@drawable/rounded_outline"
                        tools:src="@drawable/profile_bg_orange"
                        android:contentDescription="@string/account"/>

                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/settings_profile_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:textColor="?attr/textColor"
                    android:textSize="18sp"
                    android:textStyle="normal"
                    tools:text="Quick Brown Fox" />
            </LinearLayout>

            <TextView
                android:id="@+id/settings_general"

                style="@style/SettingsItem"
                android:nextFocusDown="@id/settings_player"
                android:text="@string/category_general" />

            <TextView
                android:id="@+id/settings_player"
                style="@style/SettingsItem"

                android:nextFocusUp="@id/settings_general"
                android:nextFocusDown="@id/settings_providers"
                android:text="@string/category_player" />

            <TextView
                android:id="@+id/settings_providers"
                style="@style/SettingsItem"

                android:nextFocusUp="@id/settings_player"
                android:nextFocusDown="@id/settings_ui"
                android:text="@string/category_providers" />

            <TextView
                android:id="@+id/settings_ui"
                style="@style/SettingsItem"

                android:nextFocusUp="@id/settings_providers"
                android:nextFocusDown="@id/settings_updates"
                android:text="@string/category_ui" />

            <TextView
                android:id="@+id/settings_updates"
                style="@style/SettingsItem"

                android:nextFocusUp="@id/settings_ui"
                android:nextFocusDown="@id/settings_credits"
                android:text="@string/category_updates" />

            <TextView
                android:id="@+id/settings_credits"
                style="@style/SettingsItem"
                android:nextFocusUp="@id/settings_updates"
                android:text="@string/category_account" />

            <TextView
                android:id="@+id/settings_extensions"
                style="@style/SettingsItem"
                android:nextFocusUp="@id/settings_credits"
                android:text="@string/extensions" />

            <LinearLayout
                android:id="@+id/app_version_info"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/version_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:text="@string/app_version"
                    android:textColor="?attr/textColor" />

                <TextView
                    android:id="@+id/delimiter0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="•"
                    android:textColor="?attr/textColor" />

                <TextView
                    android:id="@+id/commit_hash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:text="@string/commit_hash"
                    android:textColor="?attr/textColor" />

                <TextView
                    android:id="@+id/delimiter1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="•"
                    android:textColor="?attr/textColor" />

                <TextView
                    android:id="@+id/build_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:textColor="?attr/textColor"
                    tools:text="21/03/2024 09:02 pm"/>
            </LinearLayout>

        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>