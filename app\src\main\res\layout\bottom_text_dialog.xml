<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/dialog_title"
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp"
        android:textStyle="bold"
        android:textSize="20sp"
        android:textColor="?attr/textColor"
        android:layout_width="match_parent"
        android:layout_rowWeight="1"
        tools:text="Test"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/dialog_text"
        android:textAppearance="?android:attr/textAppearanceListItem"
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
        android:paddingTop="10dp"
        android:requiresFadingEdge="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_rowWeight="1" />

</LinearLayout>
