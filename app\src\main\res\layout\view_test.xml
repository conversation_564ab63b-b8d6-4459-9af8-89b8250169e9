<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/Widget.Material3.CardView.Elevated"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="?attr/primaryBlackBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="10dp">

        <LinearLayout
            android:id="@+id/main_test_section"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/outline_drawable_less"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="10dp"
            android:paddingVertical="10dp">

            <TextView
                android:id="@+id/main_test_header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="17sp"
                tools:text="Homepage test" />

            <TextView
                android:id="@+id/main_test_section_progress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textSize="17sp"
                tools:text="67 / 120 " />
        </LinearLayout>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            app:cardBackgroundColor="?attr/primaryGrayBackground"
            app:cardCornerRadius="@dimen/rounded_image_radius">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/passed_test_section"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/outline_drawable_less"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Tests passed"
                        android:textSize="17sp" />

                    <TextView
                        android:id="@+id/passed_test_section_progress"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textSize="17sp"
                        tools:text="55" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/failed_test_section"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/outline_drawable_less"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Tests failed"
                        android:textSize="17sp" />

                    <TextView
                        android:id="@+id/failed_test_section_progress"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textSize="17sp"
                        tools:text="12" />
                </LinearLayout>

            </LinearLayout>


        </androidx.cardview.widget.CardView>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/tests_play_pause"
            style="@style/WhiteButton"
            android:layout_width="wrap_content"
            android:layout_margin="10dp"
            android:text="@string/start"
            app:icon="@drawable/ic_baseline_play_arrow_24">

        </com.google.android.material.button.MaterialButton>

    </LinearLayout>


    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/test_total_progress"
        style="@android:style/Widget.Material.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="-1.5dp"
        android:progressBackgroundTint="?attr/colorPrimary"
        android:progressTint="?attr/colorPrimary"
        android:visibility="gone"
        tools:progress="50" />

</androidx.cardview.widget.CardView>
